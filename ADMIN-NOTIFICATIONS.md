# Admin Notification System

A complete real-time notification system for the Hive Campus admin dashboard.

## Features

### 🔔 Real-time Notifications
- Live updates via Firestore listeners
- Unread count badge in admin header
- Grouped notifications by type
- Mark as read/unread functionality
- Clear all notifications

### 📱 Notification Types
- **new_user** - New user registrations
- **new_listing** - New marketplace listings
- **purchase** - Completed purchases
- **warning** - System warnings
- **error** - System errors
- **stripe_webhook** - Stripe webhook issues
- **payment_failed** - Payment failures
- **suspicious_activity** - Suspicious user activity
- **listing_flagged** - User-flagged listings
- **user_blocked** - User blocks/suspensions
- **system_alert** - General system alerts
- **maintenance** - Maintenance notices
- **security** - Security alerts
- **performance** - Performance issues

### 🚀 Auto-Generated Notifications

#### Firebase Auth Triggers
- **User Registration**: Automatically creates notification when new user signs up

#### Firestore Triggers
- **New Listings**: Automatically creates notification when listing is added to `/listings`
- **New Orders**: Automatically creates notification when order is added to `/orders`

#### System Monitoring
- **Health Checks**: Scheduled function runs every 30 minutes to check for issues
- **Failed Payments**: Alerts when multiple payment failures detected
- **Performance Issues**: Monitors for slow response times

## Components

### AdminNotifications.tsx
Main notifications page with:
- Real-time notification list
- Filtering by type and read status
- Grouping by type or date
- Click to navigate to related pages
- Mark all as read / Clear all buttons

### AdminNotificationService.ts
Service class providing:
- Real-time subscription to notifications
- Unread count tracking
- Mark as read functionality
- Create notifications
- Batch operations

### AdminHeader.tsx
Updated header with:
- Notification bell icon
- Real-time unread count badge
- Click to navigate to notifications page

## Firebase Functions

### Auto-Triggers
```typescript
// User registration trigger
export const onUserCreate = functions.auth.user().onCreate(...)

// New listing trigger  
export const onListingCreate = functions.firestore.document('listings/{listingId}').onCreate(...)

// New order trigger
export const onOrderCreate = functions.firestore.document('orders/{orderId}').onCreate(...)
```

### Callable Functions
```typescript
// Create warning notification
export const createWarningNotification = functions.https.onCall(...)

// Create system alert
export const createSystemAlert = functions.https.onCall(...)
```

### Scheduled Functions
```typescript
// System health monitoring
export const systemHealthCheck = functions.pubsub.schedule('every 30 minutes').onRun(...)
```

## Usage

### Frontend - Create Notifications
```typescript
import AdminNotificationService from '../services/AdminNotificationService';

const notificationService = AdminNotificationService.getInstance();

// Create a notification
await notificationService.createNotification(
  'warning',
  'This is a warning message',
  'Warning Title',
  { severity: 'high', userId: 'user123' }
);

// Create via utility functions
import { reportSuspiciousActivity } from '../utils/adminNotifications';

await reportSuspiciousActivity(
  'user123',
  'Multiple failed login attempts',
  { attemptCount: 5 }
);
```

### Backend - Firebase Functions
```typescript
import { notifyAdmin } from './adminNotifications';

// From any Firebase Function
await notifyAdmin(
  'error',
  'Database connection failed',
  'System Error',
  { severity: 'critical', context: 'user-service' }
);
```

## Data Structure

### Firestore Collection: `adminNotifications`
```typescript
interface AdminNotification {
  id?: string;
  type: AdminNotificationType;
  message: string;
  title?: string;
  createdAt: Timestamp;
  read: boolean;
  readAt?: Timestamp;
  metadata?: {
    userId?: string;
    userName?: string;
    userEmail?: string;
    listingId?: string;
    listingTitle?: string;
    orderId?: string;
    orderAmount?: number;
    errorCode?: string;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    actionUrl?: string;
    relatedData?: Record<string, any>;
  };
}
```

## Navigation

### Routes
- `/admin/notifications` - Main notifications page
- Accessible via bell icon in admin header
- Also available in admin sidebar navigation

### Smart Navigation
Clicking notifications automatically navigates to relevant pages:
- **new_user** → `/admin/users?search={userId}`
- **new_listing** → `/admin/listings?search={listingId}`
- **purchase** → `/admin/transactions?search={orderId}`
- **listing_flagged** → `/admin/reports`
- **user_blocked** → `/admin/users`

## Security

### Firestore Security Rules
```javascript
// Only admins can read/write admin notifications
match /adminNotifications/{notificationId} {
  allow read, write: if request.auth != null && 
    request.auth.token.admin == true;
}
```

### Function Security
- `createWarningNotification` requires admin authentication
- `createSystemAlert` allows system calls for internal alerts
- All functions validate input parameters

## Testing

### Development Mode
In development, the admin overview page includes test buttons to create sample notifications:
- Test New User notification
- Test New Listing notification  
- Test Purchase notification
- Test Warning notification
- Test Error notification

### Manual Testing
```typescript
// Test via browser console
const service = AdminNotificationService.getInstance();
await service.createNotification('warning', 'Test notification');
```

## Deployment

### Firebase Functions
```bash
cd functions
npm run deploy
```

### Frontend
The notification system is automatically included when deploying the admin dashboard.

## Monitoring

The system includes self-monitoring capabilities:
- Failed notification creation is logged
- System health checks run automatically
- Performance metrics are tracked
- Error notifications are created for system issues

## Future Enhancements

- Email notifications for critical alerts
- Push notifications for mobile admins
- Notification templates and customization
- Advanced filtering and search
- Notification analytics and reporting
- Integration with external monitoring tools
