# Hive Campus - Testing Guide

This guide outlines the testing procedures for all features implemented in the Hive Campus application.

## 🧪 Testing Overview

### Features to Test
1. ✅ User Profile System Enhancement
2. ✅ Listings Management System
3. ✅ Mobile-Optimized Chat System
4. ✅ Dropdown UX Improvements
5. ✅ Order History System
6. ✅ Feedback and Complaint System
7. ✅ Profile Dropdown Menu Enhancement
8. ✅ Permission Enforcement
9. ✅ Firebase Functions and Security Rules
10. 🔥 **NEW: Stripe Webhook Secret Update Testing**

---

## 🔥 **URGENT: Stripe Webhook Testing (Updated Secret)**

### ⚡ **CRITICAL FIRST STEP - Update Stripe Dashboard**

**🔗 URL**: https://dashboard.stripe.com/webhooks

**Update your webhook configuration:**
- **Webhook URL**: `https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook`
- **New Webhook Secret**: `whsec_Ggd0wEt8z8NzRV5kyEyvXH2iB1xrWSZq`
- **Events**: Ensure `checkout.session.completed` is selected

### 🧪 **Quick Webhook Validation**

#### Test 1: Basic Endpoint Check
```powershell
Invoke-WebRequest -Uri "https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook" -Method GET
```
**Expected**: `400 Bad Request` with "No Stripe signature" ✅

#### Test 2: Verify New Secret in Use
```bash
firebase functions:log --only stripeWebhook | Select-String "webhook secret"
```
**Expected**: `Using webhook secret: whsec_Ggd0wEt8z8NzRV...` ✅

#### Test 3: End-to-End Payment Test
1. **Start dev server**: `npm run dev`
2. **Create test purchase** on any listing
3. **Use test card**: `4242 4242 4242 4242`
4. **Monitor logs**: `firebase functions:log --only stripeWebhook --follow`

**Expected Webhook Log Sequence**:
```
=== STRIPE WEBHOOK RECEIVED ===
Webhook signature verified successfully
Processing webhook event: checkout.session.completed
Generated secret code: [6-digit-code]
Order updated successfully
Notifications sent successfully
```

#### Test 4: Verify Database Updates
**Check Firestore**: https://console.firebase.google.com/project/h1c1-798a8/firestore
- Order status: `pending_payment` → `payment_completed`
- Secret code generated and stored
- User notifications created

### 🚨 **Troubleshooting**
- **400 Webhook Error**: Update Stripe Dashboard with new secret
- **No webhook events**: Check Stripe Dashboard webhook delivery logs
- **Orders not updating**: Check Firebase Functions logs for errors

### ✅ **Success Criteria**
- [ ] Stripe Dashboard updated with new secret
- [ ] Webhook endpoint responding correctly
- [ ] Test payments complete successfully  
- [ ] Orders update to `payment_completed`
- [ ] Secret codes generated
- [ ] User notifications sent

---

## 📱 Mobile Testing Checklist

### Profile System
- [ ] Profile editing works on mobile devices
- [ ] Image upload functions properly on mobile
- [ ] Bio, graduation year, and major fields are accessible
- [ ] Public profile view displays correctly
- [ ] Edit/Save buttons are properly sized for touch

### Listings Management
- [ ] Listing editing interface is mobile-friendly
- [ ] Price editing works with mobile keyboards
- [ ] Free listing toggle is easily accessible
- [ ] Condition dropdown functions properly
- [ ] Image upload works on mobile devices
- [ ] Delete confirmation is clear on mobile

### Chat System
- [ ] Chat interface fits mobile screen properly
- [ ] Message bubbles display correctly
- [ ] Auto-scroll works on mobile
- [ ] Keyboard doesn't interfere with chat input
- [ ] Voice/video call icons are removed
- [ ] Chat initiation from listings works

### Dropdowns
- [ ] All dropdowns close on scroll
- [ ] Dropdowns close when selecting options
- [ ] Touch interactions work properly
- [ ] Dropdowns don't overflow screen boundaries

### Order History
- [ ] Order history page is mobile responsive
- [ ] Analytics cards display properly
- [ ] Order list is scrollable and readable
- [ ] Filter controls are accessible

### Feedback/Complaint Modals
- [ ] Modals display properly on mobile
- [ ] Form fields are accessible
- [ ] File upload works on mobile
- [ ] Submit buttons are properly sized

## 🖥️ Desktop Testing Checklist

### Profile System
- [ ] Profile editing interface is intuitive
- [ ] Image upload with drag-and-drop works
- [ ] All form fields validate properly
- [ ] Save/Cancel buttons function correctly
- [ ] Public profile view shows appropriate information

### Listings Management
- [ ] Full CRUD operations work for listings
- [ ] Price editing allows increase/decrease
- [ ] Free listing toggle functions
- [ ] Condition dropdown shows all options
- [ ] Image management works properly
- [ ] Delete confirmation prevents accidental deletion

### Chat System
- [ ] Chat interface is properly sized
- [ ] Message history loads correctly
- [ ] Auto-scroll functions smoothly
- [ ] File sharing works properly
- [ ] Chat initiation from listings is seamless

### Dropdowns
- [ ] All dropdowns close on scroll
- [ ] Selection closes dropdowns immediately
- [ ] Click outside closes dropdowns
- [ ] Keyboard navigation works

### Order History
- [ ] All order statuses display correctly
- [ ] Analytics are accurate
- [ ] Filtering works properly
- [ ] Export functionality works
- [ ] Order details are comprehensive

## 🔒 Security Testing

### Permission Enforcement
- [ ] Users can only edit their own profiles
- [ ] Users can only edit/delete their own listings
- [ ] Public profiles show limited information
- [ ] Chat access is restricted to participants
- [ ] Order access is restricted to buyer/seller
- [ ] Admin functions require admin role

### Firebase Security Rules
- [ ] Firestore rules prevent unauthorized access
- [ ] Users collection allows public read, owner write
- [ ] Listings collection enforces ownership
- [ ] Chats collection restricts to participants
- [ ] Orders collection restricts to buyer/seller
- [ ] Feedback/Complaints restrict to owner/admin

### Frontend Validation
- [ ] Permission checks prevent unauthorized UI access
- [ ] Error messages are user-friendly
- [ ] Unauthorized actions are blocked
- [ ] Admin features are hidden from non-admins

## 🔧 Functional Testing

### User Profile Enhancement
- [ ] Profile image upload works
- [ ] Bio editing saves correctly
- [ ] Graduation year validation works
- [ ] Major field saves properly
- [ ] Public profile shows correct information
- [ ] Edit restrictions work properly

### Listings Management
- [ ] Create new listings works
- [ ] Edit existing listings saves changes
- [ ] Delete listings removes them
- [ ] Price changes are reflected
- [ ] Free listing toggle works
- [ ] Condition dropdown saves selection
- [ ] Image upload/removal works

### Chat System
- [ ] Chat initiation from listings works
- [ ] Message sending/receiving works
- [ ] Auto-scroll functions
- [ ] Message timestamps are accurate
- [ ] File sharing works
- [ ] Chat history persists

### Order History
- [ ] Order data displays correctly
- [ ] Status updates reflect properly
- [ ] Analytics calculations are accurate
- [ ] Filtering works as expected
- [ ] Export generates correct data

### Feedback System
- [ ] Feedback submission works
- [ ] Rating system functions
- [ ] Category selection works
- [ ] File attachments upload
- [ ] Submission confirmation appears

### Complaint System
- [ ] Complaint submission works
- [ ] Category selection functions
- [ ] Priority levels work
- [ ] Order ID linking works
- [ ] File attachments upload

## 🚀 Performance Testing

### Page Load Times
- [ ] Profile page loads quickly
- [ ] Order history loads efficiently
- [ ] Chat loads without delay
- [ ] Modals open smoothly

### Mobile Performance
- [ ] Smooth scrolling on mobile
- [ ] Quick dropdown responses
- [ ] Fast image loading
- [ ] Efficient chat updates

### Database Performance
- [ ] Efficient queries for user data
- [ ] Optimized listing retrieval
- [ ] Fast chat message loading
- [ ] Quick order history fetching

## 🐛 Bug Testing

### Common Issues to Check
- [ ] Memory leaks in chat auto-scroll
- [ ] Dropdown positioning issues
- [ ] Modal z-index conflicts
- [ ] Image upload failures
- [ ] Form validation errors
- [ ] Permission check failures

### Error Handling
- [ ] Network errors are handled gracefully
- [ ] Invalid data shows appropriate errors
- [ ] Permission errors are user-friendly
- [ ] File upload errors are clear
- [ ] Database errors don't crash app

## 📊 User Experience Testing

### Navigation
- [ ] Profile dropdown menu is intuitive
- [ ] Order history is easy to find
- [ ] Feedback/complaint options are accessible
- [ ] Back navigation works properly

### Accessibility
- [ ] All buttons are properly labeled
- [ ] Form fields have appropriate labels
- [ ] Error messages are clear
- [ ] Color contrast is sufficient
- [ ] Keyboard navigation works

### Responsiveness
- [ ] All pages work on different screen sizes
- [ ] Modals adapt to screen size
- [ ] Dropdowns position correctly
- [ ] Text remains readable

## ✅ Test Completion Checklist

### Before Deployment
- [ ] All mobile tests pass
- [ ] All desktop tests pass
- [ ] Security tests pass
- [ ] Performance is acceptable
- [ ] No critical bugs found
- [ ] User experience is smooth

### Post-Deployment Monitoring
- [ ] Monitor error rates
- [ ] Check performance metrics
- [ ] Verify security logs
- [ ] Monitor user feedback
- [ ] Track feature usage

## 🔍 Testing Tools

### Recommended Testing Approaches
1. **Manual Testing**: Test all features manually on different devices
2. **Browser Testing**: Test on Chrome, Firefox, Safari, Edge
3. **Device Testing**: Test on various mobile devices and screen sizes
4. **Network Testing**: Test on different network speeds
5. **User Testing**: Have real users test the features

### Debugging Tools
- Browser Developer Tools
- React Developer Tools
- Firebase Console
- Network monitoring tools
- Performance profiling tools

## 📝 Test Results Documentation

### Test Report Template
```
Feature: [Feature Name]
Test Date: [Date]
Tester: [Name]
Device/Browser: [Details]
Status: [Pass/Fail]
Issues Found: [List any issues]
Notes: [Additional observations]
```

### Issue Tracking
- Document all bugs found
- Prioritize issues by severity
- Track resolution status
- Verify fixes work properly

---

**Note**: This testing guide should be followed thoroughly before deploying any new features to production. All tests should pass before considering the implementation complete.
