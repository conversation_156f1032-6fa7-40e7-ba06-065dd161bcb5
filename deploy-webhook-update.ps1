# Quick webhook secret update deployment
Write-Host "🔄 Updating Stripe webhook secret..." -ForegroundColor Cyan

# Try deploying just the functions
Write-Host "📦 Deploying functions..." -ForegroundColor Yellow

try {
    firebase deploy --only functions --force
    Write-Host "✅ Functions deployed successfully!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Standard deployment failed, trying alternative approach..." -ForegroundColor Yellow
    
    # Try deploying with timeout increase
    $env:FIREBASE_FUNCTIONS_TIMEOUT = "540"
    firebase deploy --only functions --debug
}

Write-Host "🧪 Testing webhook endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook" -Method GET -ErrorAction SilentlyContinue
} catch {
    if ($_.Exception.Message -like "*No Stripe signature*") {
        Write-Host "✅ Webhook endpoint is responding correctly!" -ForegroundColor Green
    } else {
        Write-Host "❌ Unexpected webhook response: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Webhook secret update complete!" -ForegroundColor Cyan