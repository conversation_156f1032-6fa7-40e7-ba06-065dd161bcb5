# Firebase Functions Deployment Strategy for Hive Campus

## ✅ Successfully Deployed Functions

### Core Functions (Currently Live)
1. **testFunction** - Basic health check endpoint
2. **fixAdminUser** - Admin user role management
3. **createUserRecord** - User registration with .edu validation
4. **stripeWebhook** - Stripe webhook endpoint (placeholder)
5. **getWalletData** - Wallet data retrieval with auto-creation

## 🔄 Next Priority Functions to Add

### Phase 1: Authentication & Admin (High Priority)
- [ ] `setAdminPin` - Admin PIN setup
- [ ] `verifyAdminPin` - Admin PIN verification
- [ ] `refreshUserToken` - Token refresh for custom claims

### Phase 2: Core Marketplace (High Priority)
- [ ] `getListings` - Retrieve marketplace listings
- [ ] `createListing` - Create new listings
- [ ] `editListing` - Edit existing listings
- [ ] `deleteListing` - Delete listings

### Phase 3: Payment & Wallet (Medium Priority)
- [ ] `stripeApi` - Enhanced Stripe operations
- [ ] `grantWalletCredit` - Admin wallet credit granting
- [ ] `processReferralCode` - Referral system
- [ ] `redeemSecretCode` - Delivery confirmation

### Phase 4: Communication (Medium Priority)
- [ ] `sendMessage` - Chat messaging
- [ ] `getMessages` - Retrieve chat messages
- [ ] `createOrGetChat` - Chat creation/retrieval

### Phase 5: Advanced Features (Lower Priority)
- [ ] `uploadFile` - File upload handling
- [ ] `getShippingRatesCallable` - Shipping calculations
- [ ] `reportIssue` - Issue reporting system

## 🛠️ Deployment Best Practices Learned

### 1. Lazy Loading Pattern
```javascript
// ✅ GOOD - Load dependencies inside function
exports.myFunction = functions.https.onCall(async (data, context) => {
  const admin = require('firebase-admin');
  if (!admin.apps.length) {
    admin.initializeApp();
  }
  // ... function logic
});

// ❌ BAD - Load dependencies at module level
const admin = require('firebase-admin');
admin.initializeApp();
```

### 2. Memory Optimization
- Use appropriate memory settings for each function
- Start with 128MB for simple functions
- Use 256MB for database operations
- Use 512MB+ for heavy processing (webhooks, file uploads)

### 3. Timeout Configuration
- Set realistic timeouts based on function complexity
- 30s for simple operations
- 60s for database operations
- 300s for webhooks and external API calls

### 4. Incremental Deployment
- Add 1-3 functions at a time
- Test each deployment before adding more
- Keep functions focused and single-purpose

## 🚨 Common Issues to Avoid

1. **Module-level imports** of heavy dependencies
2. **Synchronous operations** during initialization
3. **Too many functions** in a single deployment
4. **Missing error handling** in async operations
5. **Incorrect memory/timeout** configurations

## 📝 Function Template

```javascript
// Function description - lazy load dependencies
exports.functionName = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      // Lazy load dependencies
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }

      // Function logic here
      
      return { success: true, data: result };
    } catch (error) {
      console.error('Error in functionName:', error);
      throw new functions.https.HttpsError('internal', 'Function failed');
    }
  });
```
