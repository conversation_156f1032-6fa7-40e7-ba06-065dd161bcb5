// Essential Firebase Functions for Hive Campus - Listings Only
const functions = require('firebase-functions/v1');

// Lazy load admin to avoid initialization timeouts
let admin;
const getAdmin = () => {
  if (!admin) {
    admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }
  }
  return admin;
};

// Simple test function
exports.testFunction = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onRequest(async (req, res) => {
    res.json({
      success: true,
      message: 'Complete Hive Campus Functions deployed successfully',
      timestamp: new Date().toISOString(),
      version: '5.0.0-COMPLETE',
      status: 'WORKING'
    });
  });

// Helper function to verify authentication
const verifyAuth = (context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  return context.auth;
};

// Helper function to handle errors
const handleError = (error) => {
  console.error('Function error:', error);
  if (error instanceof functions.https.HttpsError) {
    throw error;
  }
  throw new functions.https.HttpsError('internal', 'Internal server error');
};

// ===== LISTING FUNCTIONS =====

// Create a new listing
exports.createListing = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      const auth = verifyAuth(context);
      const admin = getAdmin();

      const {
        title,
        description,
        price,
        category,
        condition,
        type,
        imageURLs,
        visibility = 'university',
        deliveryMethod = 'in_person',
        shippingOptions,
        // Rent-specific fields
        rentalPeriod,
        weeklyPrice,
        monthlyPrice,
        startDate,
        endDate,
        // Auction-specific fields
        startingBid,
        auctionStartDate,
        auctionStartTime,
        auctionEndDate,
        auctionEndTime,
        auctionDuration
      } = data;

      // Get user's university
      const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
      const userData = userDoc.data();

      if (!userData) {
        throw new functions.https.HttpsError('not-found', 'User profile not found');
      }

      // Create listing object
      const listingData = {
        title,
        description,
        price: type === 'auction' ? (startingBid || 0) : price,
        category,
        condition,
        type,
        imageURLs: imageURLs || [],
        visibility,
        deliveryMethod,
        shippingOptions: shippingOptions || null,
        ownerId: auth.uid,
        ownerName: userData.name,
        ownerEmail: userData.email,
        university: userData.university,
        status: 'active',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        views: 0,
        favorites: 0,
        // Rent-specific fields
        ...(type === 'rent' && {
          rentalPeriod,
          weeklyPrice,
          monthlyPrice,
          startDate,
          endDate
        }),
        // Auction-specific fields
        ...(type === 'auction' && {
          startingBid,
          currentBid: startingBid || 0,
          auctionStartDate,
          auctionStartTime,
          auctionEndDate,
          auctionEndTime,
          auctionDuration,
          bidders: [],
          highestBidder: null
        })
      };

      // Add the listing to Firestore
      const listingRef = await admin.firestore().collection('listings').add(listingData);

      return {
        success: true,
        data: {
          id: listingRef.id,
          ...listingData
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Get a single listing by ID
exports.getListingById = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30
  })
  .https.onCall(async (data, context) => {
    try {
      verifyAuth(context);

      const { listingId } = data;

      if (!listingId) {
        throw new functions.https.HttpsError('invalid-argument', 'Listing ID is required');
      }

      const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();

      if (!listingDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Listing not found');
      }

      const listing = listingDoc.data();

      // Increment view count
      await admin.firestore().collection('listings').doc(listingId).update({
        views: admin.firestore.FieldValue.increment(1)
      });

      return {
        success: true,
        data: {
          id: listingId,
          ...listing
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Get listings with filtering
exports.getListings = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      verifyAuth(context);

      const {
        university,
        category,
        type,
        condition,
        minPrice,
        maxPrice,
        ownerId,
        status = 'active',
        limit = 20,
        lastVisible
      } = data;

      // Get current user's university for visibility filtering
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      const currentUserUniversity = userDoc.data()?.university;

      let query = admin.firestore().collection('listings')
        .where('status', '==', status)
        .orderBy('createdAt', 'desc');

      // Apply filters if provided
      if (university) {
        query = query.where('university', '==', university);
      }

      if (category) {
        query = query.where('category', '==', category);
      }

      if (type) {
        query = query.where('type', '==', type);
      }

      if (condition) {
        query = query.where('condition', '==', condition);
      }

      if (ownerId) {
        query = query.where('ownerId', '==', ownerId);
      }

      // Apply limit
      query = query.limit(limit);

      // Apply pagination if lastVisible is provided
      if (lastVisible) {
        const lastDoc = await admin.firestore().collection('listings').doc(lastVisible).get();
        if (lastDoc.exists) {
          query = query.startAfter(lastDoc);
        }
      }

      const snapshot = await query.get();
      const listings = [];

      snapshot.forEach((doc) => {
        const listing = doc.data();

        // Apply visibility filtering
        if (listing.visibility === 'university' && listing.university !== currentUserUniversity) {
          return; // Skip this listing
        }

        // Apply price filtering (done client-side since Firestore doesn't support range queries with other filters)
        if (minPrice !== undefined && listing.price < minPrice) {
          return;
        }
        if (maxPrice !== undefined && listing.price > maxPrice) {
          return;
        }

        listings.push({
          id: doc.id,
          ...listing
        });
      });

      // Get the last document for pagination
      const lastDoc = snapshot.docs[snapshot.docs.length - 1];
      const lastVisibleId = lastDoc ? lastDoc.id : null;

      return {
        success: true,
        data: {
          listings,
          lastVisible: lastVisibleId,
          total: listings.length
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Search listings by title or description
exports.searchListings = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      verifyAuth(context);

      const { query: searchQuery, limit = 20 } = data;

      if (!searchQuery || searchQuery.trim().length === 0) {
        throw new functions.https.HttpsError('invalid-argument', 'Search query is required');
      }

      // Get current user's university for visibility filtering
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      const currentUserUniversity = userDoc.data()?.university;

      // Get all active listings (we'll filter client-side for search)
      const snapshot = await admin.firestore().collection('listings')
        .where('status', '==', 'active')
        .orderBy('createdAt', 'desc')
        .limit(100) // Get more to filter from
        .get();

      const listings = [];
      const searchTerms = searchQuery.toLowerCase().split(' ');

      snapshot.forEach((doc) => {
        const listing = doc.data();

        // Apply visibility filtering
        if (listing.visibility === 'university' && listing.university !== currentUserUniversity) {
          return;
        }

        // Search in title and description
        const title = (listing.title || '').toLowerCase();
        const description = (listing.description || '').toLowerCase();
        const category = (listing.category || '').toLowerCase();

        const matchesSearch = searchTerms.some(term =>
          title.includes(term) ||
          description.includes(term) ||
          category.includes(term)
        );

        if (matchesSearch) {
          listings.push({
            id: doc.id,
            ...listing
          });
        }
      });

      // Limit results
      const limitedListings = listings.slice(0, limit);

      return {
        success: true,
        data: {
          listings: limitedListings,
          total: limitedListings.length
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Edit an existing listing
exports.editListing = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      const auth = verifyAuth(context);

      const { listingId, ...updateData } = data;

      if (!listingId) {
        throw new functions.https.HttpsError('invalid-argument', 'Listing ID is required');
      }

      // Get the existing listing
      const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();

      if (!listingDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Listing not found');
      }

      const listing = listingDoc.data();

      // Check if user owns the listing
      if (listing.ownerId !== auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'You can only edit your own listings');
      }

      // Prepare update data
      const updates = {
        ...updateData,
        updatedAt: admin.firestore.Timestamp.now()
      };

      // Remove undefined values
      Object.keys(updates).forEach(key => {
        if (updates[key] === undefined) {
          delete updates[key];
        }
      });

      // Update the listing
      await admin.firestore().collection('listings').doc(listingId).update(updates);

      // Get updated listing
      const updatedDoc = await admin.firestore().collection('listings').doc(listingId).get();

      return {
        success: true,
        data: {
          id: listingId,
          ...updatedDoc.data()
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Delete a listing
exports.deleteListing = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30
  })
  .https.onCall(async (data, context) => {
    try {
      const auth = verifyAuth(context);

      const { listingId } = data;

      if (!listingId) {
        throw new functions.https.HttpsError('invalid-argument', 'Listing ID is required');
      }

      // Get the existing listing
      const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();

      if (!listingDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Listing not found');
      }

      const listing = listingDoc.data();

      // Check if user owns the listing
      if (listing.ownerId !== auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'You can only delete your own listings');
      }

      // Delete the listing
      await admin.firestore().collection('listings').doc(listingId).delete();

      return {
        success: true,
        message: 'Listing deleted successfully'
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Stripe webhook handler - lazy load everything
exports.stripeWebhook = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 300
  })
  .https.onRequest(async (req, res) => {
    try {
      console.log('=== STRIPE WEBHOOK RECEIVED ===');
      console.log('Headers:', req.headers);
      console.log('Body type:', typeof req.body);
      
      // Lazy load dependencies
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }
      
      const Stripe = require('stripe');
      
      // Get Stripe configuration from Firebase Functions config
      const stripeConfig = functions.config().stripe;
      const stripeSecretKey = stripeConfig?.secret_key || process.env.STRIPE_SECRET_KEY;
      const webhookSecret = stripeConfig?.webhook_secret || process.env.STRIPE_WEBHOOK_SECRET;
      
      const stripe = new Stripe(stripeSecretKey, {
        apiVersion: '2024-06-20',
      });

      console.log('Stripe initialized with key:', stripeSecretKey.substring(0, 20) + '...');
      console.log('Using webhook secret:', webhookSecret.substring(0, 20) + '...');

      const sig = req.headers['stripe-signature'];

      if (!sig) {
        console.error('No Stripe signature found');
        res.status(400).send('No Stripe signature');
        return;
      }

      let event;
      try {
        event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
        console.log('Webhook signature verified successfully');
      } catch (err) {
        console.error('Webhook signature verification failed:', err.message);
        res.status(400).send(`Webhook Error: ${err.message}`);
        return;
      }

      console.log('Processing webhook event:', event.type);
      console.log('Event data:', JSON.stringify(event.data, null, 2));

      // Generate 6-digit secret code
      const generateSecretCode = () => {
        return Math.floor(100000 + Math.random() * 900000).toString();
      };

      // Handle checkout session completed
      if (event.type === 'checkout.session.completed') {
        const session = event.data.object;
        const metadata = session.metadata;

        console.log('Checkout session completed:', session.id);
        console.log('Session metadata:', metadata);

        if (metadata && metadata.orderId) {
          const orderId = metadata.orderId;
          console.log('Processing order:', orderId);
          
          const orderRef = admin.firestore().collection('orders').doc(orderId);
          const orderDoc = await orderRef.get();

          if (orderDoc.exists) {
            const secretCode = generateSecretCode();
            console.log('Generated secret code:', secretCode);

            // Update order with payment completion and secret code
            await orderRef.update({
              status: 'payment_completed',
              stripeSessionId: session.id,
              stripePaymentIntentId: session.payment_intent,
              secretCode: secretCode,
              paymentCompletedAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });

            console.log('Order updated successfully:', orderId);
            
            // Get order data for notifications
            const orderData = orderDoc.data();
            
            // Create notifications for buyer and seller
            try {
              // Notify buyer
              await admin.firestore().collection('users').doc(orderData.buyerId).collection('notifications').add({
                type: 'payment_completed',
                title: 'Payment Successful',
                message: `Your payment for "${orderData.title}" has been processed. Secret code: ${secretCode}`,
                orderId: orderId,
                secretCode: secretCode,
                read: false,
                createdAt: admin.firestore.Timestamp.now()
              });

              // Notify seller
              await admin.firestore().collection('users').doc(orderData.sellerId).collection('notifications').add({
                type: 'order_received',
                title: 'New Order Received',
                message: `You have received a new order for "${orderData.title}". Payment has been processed.`,
                orderId: orderId,
                read: false,
                createdAt: admin.firestore.Timestamp.now()
              });

              console.log('Notifications sent successfully');
            } catch (notificationError) {
              console.error('Error sending notifications:', notificationError);
            }
          } else {
            console.error('Order not found:', orderId);
          }
        } else {
          console.error('No orderId in session metadata');
        }
      }

      res.status(200).json({ received: true });
    } catch (error) {
      console.error('Error processing webhook:', error);
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  });

// Stripe API Express app for handling checkout and payments
exports.stripeApi = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 300
  })
  .https.onRequest(async (req, res) => {
    try {
      // Lazy load dependencies
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }
      
      const express = require('express');
      const cors = require('cors');
      
      const app = express();
      app.use(cors({ origin: true }));
      app.use(express.json());

      // Create checkout session endpoint
      app.post('/create-checkout-session', async (req, res) => {
        try {
          console.log('=== CREATE CHECKOUT SESSION ===');
          console.log('Request headers:', req.headers);
          console.log('Request body:', req.body);

          // Verify Firebase authentication
          const authHeader = req.headers.authorization;
          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.error('No authorization header found');
            return res.status(403).json({ error: 'Unauthorized - Missing authorization header' });
          }

          const idToken = authHeader.split('Bearer ')[1];
          let decodedToken;
          
          try {
            decodedToken = await admin.auth().verifyIdToken(idToken);
            console.log('Token verified for user:', decodedToken.uid);
          } catch (authError) {
            console.error('Token verification failed:', authError);
            return res.status(403).json({ error: 'Unauthorized - Invalid token' });
          }

          const { listingId, buyerId, useWalletBalance, orderDetails } = req.body;

          // Verify the authenticated user matches the buyerId
          if (decodedToken.uid !== buyerId) {
            console.error('User ID mismatch:', decodedToken.uid, 'vs', buyerId);
            return res.status(403).json({ error: 'Unauthorized - User ID mismatch' });
          }

          if (!listingId || !buyerId) {
            console.error('Missing required fields:', { listingId, buyerId });
            return res.status(400).json({ error: 'Missing required fields: listingId and buyerId' });
          }

          // Get listing details
          const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
          if (!listingDoc.exists) {
            console.error('Listing not found:', listingId);
            return res.status(404).json({ error: 'Listing not found' });
          }

          const listing = listingDoc.data();

          if (!listing) {
            console.error('Listing data not found:', listingId);
            return res.status(404).json({ error: 'Listing data not found' });
          }

          console.log('Listing found:', listing.title, 'Price:', listing.price);

          const price = orderDetails?.price || listing.price;

          // Calculate platform fee
          const platformFeeRate = listing.category === 'textbooks' ? 0.08 : 0.10;
          const platformFee = price * platformFeeRate;

          // Handle wallet credit
          let walletAmountUsed = 0;
          if (useWalletBalance && orderDetails?.appliedWalletCredit) {
            walletAmountUsed = Math.min(orderDetails.appliedWalletCredit, price);
          }

          const finalAmount = Math.max(0.50, price - walletAmountUsed); // Minimum $0.50 charge

          console.log('Pricing calculation:', {
            originalPrice: price,
            platformFee,
            walletAmountUsed,
            finalAmount
          });

          // Create order document first
          const orderRef = admin.firestore().collection('orders').doc();
          const order = {
            id: orderRef.id,
            buyerId,
            sellerId: listing.ownerId,
            listingId,
            amount: price,
            finalAmount,
            platformFee,
            walletAmountUsed,
            status: 'pending_payment',
            title: listing.title,
            category: listing.category,
            orderType: orderDetails?.type || 'buy',
            rentalPeriod: orderDetails?.period,
            bidAmount: orderDetails?.bidAmount,
            shippingAddress: orderDetails?.shippingAddress,
            deliveryMethod: orderDetails?.deliveryMethod || 'in_person',
            shippingFee: orderDetails?.shippingFee || 0,
            shippingPaidBy: orderDetails?.shippingPaidBy || 'buyer',
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
          };

          await orderRef.set(order);
          console.log('Order created:', orderRef.id);

          // Create Stripe checkout session
          const Stripe = require('stripe');
          const stripeConfig = functions.config().stripe;
          const stripeSecretKey = stripeConfig?.secret_key || process.env.STRIPE_SECRET_KEY;
          
          const stripe = new Stripe(stripeSecretKey, {
            apiVersion: '2024-06-20',
          });

          console.log('Creating Stripe session with key:', stripeSecretKey.substring(0, 20) + '...');

          const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [
              {
                price_data: {
                  currency: 'usd',
                  product_data: {
                    name: listing.title,
                    description: listing.description || 'Hive Campus marketplace item',
                    images: listing.images ? [listing.images[0]] : []
                  },
                  unit_amount: Math.round(finalAmount * 100) // Convert to cents
                },
                quantity: 1
              }
            ],
            mode: 'payment',
            success_url: `${process.env.FRONTEND_URL || 'https://hivecampus.app'}/order/success?session_id={CHECKOUT_SESSION_ID}&order_id=${orderRef.id}`,
            cancel_url: `${process.env.FRONTEND_URL || 'https://hivecampus.app'}/listing/${listingId}`,
            metadata: {
              orderId: orderRef.id,
              listingId,
              buyerId,
              sellerId: listing.ownerId,
              walletAmountUsed: walletAmountUsed.toString()
            },
            automatic_tax: {
              enabled: true
            },
            shipping_address_collection: orderDetails?.deliveryMethod === 'mail' ? {
              allowed_countries: ['US']
            } : undefined
          });

          console.log('Stripe session created:', session.id);

          // Update order with session ID
          await orderRef.update({
            stripeSessionId: session.id,
            updatedAt: admin.firestore.Timestamp.now()
          });

          console.log('Order updated with session ID');

          res.json({
            sessionId: session.id,
            sessionUrl: session.url
          });
        } catch (error) {
          console.error('Error creating checkout session:', error);
          res.status(500).json({ 
            error: 'Failed to create checkout session',
            details: error.message 
          });
        }
      });

      // Handle the request
      app(req, res);
    } catch (error) {
      console.error('Error in stripeApi function:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });

// Secret code redemption function
exports.redeemSecretCode = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 300
  })
  .https.onCall(async (data, context) => {
    try {
      console.log('=== REDEEM SECRET CODE ===');
      console.log('Data:', data);
      console.log('Context auth:', context.auth?.uid);

      // Lazy load admin
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }

      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { orderId, secretCode } = data;
      const userId = context.auth.uid;

      if (!orderId || !secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code are required');
      }

      // Get order document
      const orderRef = admin.firestore().collection('orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      if (!orderData) {
        throw new functions.https.HttpsError('not-found', 'Order data not found');
      }

      // Verify buyer is the one redeeming
      if (orderData.buyerId !== userId) {
        throw new functions.https.HttpsError('permission-denied', 'Only the buyer can redeem the secret code');
      }

      // Verify secret code
      if (orderData.secretCode !== secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
      }

      // Check if already redeemed
      if (orderData.status === 'completed') {
        throw new functions.https.HttpsError('failed-precondition', 'Order has already been completed');
      }

      // Calculate seller payout (subtract platform fee)
      const platformFeeRate = orderData.category === 'textbooks' ? 0.08 : 0.10;
      const platformFee = orderData.amount * platformFeeRate;
      const sellerPayout = orderData.amount - platformFee;

      // Update order status to completed
      await orderRef.update({
        status: 'completed',
        secretCodeRedeemedAt: admin.firestore.Timestamp.now(),
        sellerPayout: sellerPayout,
        platformFee: platformFee,
        completedAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });

      console.log('Secret code redeemed successfully:', orderId);

      return {
        success: true,
        message: 'Secret code redeemed successfully',
        orderId: orderId,
        sellerPayout: sellerPayout
      };
    } catch (error) {
      console.error('Error redeeming secret code:', error);
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      throw new functions.https.HttpsError('internal', 'Failed to redeem secret code');
    }
  });