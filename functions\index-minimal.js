// Minimal Firebase Functions for Hive Campus - Essential Listings Only
const functions = require('firebase-functions/v1');

// Lazy load admin to avoid initialization timeouts
let admin;
const getAdmin = () => {
  if (!admin) {
    admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }
  }
  return admin;
};

// Helper function to verify authentication
const verifyAuth = (context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  return context.auth;
};

// Helper function to handle errors
const handleError = (error) => {
  console.error('Function error:', error);
  if (error instanceof functions.https.HttpsError) {
    throw error;
  }
  throw new functions.https.HttpsError('internal', 'Internal server error');
};

// Simple test function
exports.testFunction = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onRequest(async (req, res) => {
    res.json({
      success: true,
      message: 'Minimal Hive Campus Functions deployed successfully',
      timestamp: new Date().toISOString(),
      version: '5.0.0-MINIMAL',
      status: 'WORKING'
    });
  });

// Get a single listing by ID
exports.getListingById = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30
  })
  .https.onCall(async (data, context) => {
    try {
      verifyAuth(context);
      const admin = getAdmin();
      
      const { listingId } = data;
      
      if (!listingId) {
        throw new functions.https.HttpsError('invalid-argument', 'Listing ID is required');
      }

      const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
      
      if (!listingDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Listing not found');
      }

      const listing = listingDoc.data();

      // Increment view count
      await admin.firestore().collection('listings').doc(listingId).update({
        views: admin.firestore.FieldValue.increment(1)
      });

      return {
        success: true,
        data: {
          id: listingId,
          ...listing
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Get listings with filtering
exports.getListings = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      verifyAuth(context);
      const admin = getAdmin();

      const {
        university,
        category,
        type,
        condition,
        minPrice,
        maxPrice,
        ownerId,
        status = 'active',
        limit = 20,
        lastVisible
      } = data;

      // Get current user's university for visibility filtering
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      const currentUserUniversity = userDoc.data()?.university;

      let query = admin.firestore().collection('listings')
        .where('status', '==', status)
        .orderBy('createdAt', 'desc');

      // Apply filters if provided
      if (university) {
        query = query.where('university', '==', university);
      }

      if (category) {
        query = query.where('category', '==', category);
      }

      if (type) {
        query = query.where('type', '==', type);
      }

      if (condition) {
        query = query.where('condition', '==', condition);
      }

      if (ownerId) {
        query = query.where('ownerId', '==', ownerId);
      }

      // Apply limit
      query = query.limit(limit);

      // Apply pagination if lastVisible is provided
      if (lastVisible) {
        const lastDoc = await admin.firestore().collection('listings').doc(lastVisible).get();
        if (lastDoc.exists) {
          query = query.startAfter(lastDoc);
        }
      }

      const snapshot = await query.get();
      const listings = [];

      snapshot.forEach((doc) => {
        const listing = doc.data();
        
        // Apply visibility filtering
        if (listing.visibility === 'university' && listing.university !== currentUserUniversity) {
          return; // Skip this listing
        }

        // Apply price filtering (done client-side since Firestore doesn't support range queries with other filters)
        if (minPrice !== undefined && listing.price < minPrice) {
          return;
        }
        if (maxPrice !== undefined && listing.price > maxPrice) {
          return;
        }

        listings.push({
          id: doc.id,
          ...listing
        });
      });

      // Get the last document for pagination
      const lastDoc = snapshot.docs[snapshot.docs.length - 1];
      const lastVisibleId = lastDoc ? lastDoc.id : null;

      return {
        success: true,
        data: {
          listings,
          lastVisible: lastVisibleId,
          total: listings.length
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Create a new listing
exports.createListing = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      const auth = verifyAuth(context);
      const admin = getAdmin();
      
      const {
        title,
        description,
        price,
        category,
        condition,
        type,
        imageURLs,
        visibility = 'university'
      } = data;

      // Get user's university
      const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
      const userData = userDoc.data();
      
      if (!userData) {
        throw new functions.https.HttpsError('not-found', 'User profile not found');
      }

      // Create listing object
      const listingData = {
        title,
        description,
        price,
        category,
        condition,
        type,
        imageURLs: imageURLs || [],
        visibility,
        ownerId: auth.uid,
        ownerName: userData.name,
        ownerEmail: userData.email,
        university: userData.university,
        status: 'active',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        views: 0,
        favorites: 0
      };

      // Add the listing to Firestore
      const listingRef = await admin.firestore().collection('listings').add(listingData);

      return {
        success: true,
        data: {
          id: listingRef.id,
          ...listingData
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });
