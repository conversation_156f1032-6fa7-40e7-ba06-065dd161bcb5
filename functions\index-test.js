// Ultra-minimal test function for Hive Campus
const functions = require('firebase-functions/v1');

// Simple test function
exports.testFunction = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onRequest(async (req, res) => {
    res.json({
      success: true,
      message: 'Test function deployed successfully',
      timestamp: new Date().toISOString(),
      version: '5.0.0-TEST',
      status: 'WORKING'
    });
  });
