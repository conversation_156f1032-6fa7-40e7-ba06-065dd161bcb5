// Minimal webhook-only deployment for secret update
const functions = require('firebase-functions/v1');

// Simple test function
exports.testFunction = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onRequest(async (req, res) => {
    res.json({
      success: true,
      message: 'Webhook-only deployment active',
      timestamp: new Date().toISOString(),
      version: '4.1.0-WEBHOOK-UPDATE'
    });
  });

// Stripe webhook handler with updated secret
exports.stripeWebhook = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 300
  })
  .https.onRequest(async (req, res) => {
    try {
      console.log('=== STRIPE WEBHOOK RECEIVED ===');
      console.log('Headers:', req.headers);
      console.log('Body type:', typeof req.body);
      
      // Lazy load dependencies
      const admin = require('firebase-admin');
      if (!admin.apps.length) {
        admin.initializeApp();
      }
      
      const Stripe = require('stripe');
      
      // Get Stripe configuration from Firebase Functions config
      const stripeConfig = functions.config().stripe;
      const stripeSecretKey = stripeConfig?.secret_key || process.env.STRIPE_SECRET_KEY;
      const webhookSecret = stripeConfig?.webhook_secret || process.env.STRIPE_WEBHOOK_SECRET;
      
      console.log('Using webhook secret:', webhookSecret ? webhookSecret.substring(0, 20) + '...' : 'NOT FOUND');
      
      if (!webhookSecret) {
        console.error('No webhook secret configured!');
        res.status(500).send('Webhook secret not configured');
        return;
      }
      
      const stripe = new Stripe(stripeSecretKey, {
        apiVersion: '2024-06-20',
      });

      const sig = req.headers['stripe-signature'];

      if (!sig) {
        console.error('No Stripe signature found');
        res.status(400).send('No Stripe signature');
        return;
      }

      let event;
      try {
        event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
        console.log('Webhook signature verified successfully with new secret');
      } catch (err) {
        console.error('Webhook signature verification failed:', err.message);
        res.status(400).send(`Webhook Error: ${err.message}`);
        return;
      }

      console.log('Processing webhook event:', event.type);

      // Generate 6-digit secret code
      const generateSecretCode = () => {
        return Math.floor(100000 + Math.random() * 900000).toString();
      };

      // Handle checkout session completed
      if (event.type === 'checkout.session.completed') {
        const session = event.data.object;
        const metadata = session.metadata;

        console.log('Checkout session completed:', session.id);

        if (metadata && metadata.orderId) {
          const orderId = metadata.orderId;
          console.log('Processing order:', orderId);
          
          const orderRef = admin.firestore().collection('orders').doc(orderId);
          const orderDoc = await orderRef.get();

          if (orderDoc.exists) {
            const secretCode = generateSecretCode();
            console.log('Generated secret code:', secretCode);

            // Update order with payment completion and secret code
            await orderRef.update({
              status: 'payment_completed',
              stripeSessionId: session.id,
              stripePaymentIntentId: session.payment_intent,
              secretCode: secretCode,
              paymentCompletedAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });

            console.log('Order updated successfully:', orderId);
            
            // Get order data for notifications
            const orderData = orderDoc.data();
            
            // Create notifications for buyer and seller
            try {
              // Notify buyer
              await admin.firestore().collection('users').doc(orderData.buyerId).collection('notifications').add({
                type: 'payment_completed',
                title: 'Payment Successful',
                message: `Your payment for "${orderData.title}" has been processed. Secret code: ${secretCode}`,
                orderId: orderId,
                secretCode: secretCode,
                read: false,
                createdAt: admin.firestore.Timestamp.now()
              });

              // Notify seller
              await admin.firestore().collection('users').doc(orderData.sellerId).collection('notifications').add({
                type: 'order_received',
                title: 'New Order Received',
                message: `You have received a new order for "${orderData.title}". Payment has been processed.`,
                orderId: orderId,
                read: false,
                createdAt: admin.firestore.Timestamp.now()
              });

              console.log('Notifications sent successfully');
            } catch (notificationError) {
              console.error('Error sending notifications:', notificationError);
            }
          } else {
            console.error('Order not found:', orderId);
          }
        } else {
          console.error('No orderId in session metadata');
        }
      }

      res.status(200).json({ received: true, webhookSecretUpdated: true });
    } catch (error) {
      console.error('Error processing webhook:', error);
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  });