const functions = require('firebase-functions');

exports.verifyAdminPin = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { pin } = data;

    if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
      throw new functions.https.HttpsError('invalid-argument', 'PIN must be exactly 8 digits');
    }

    const admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }

    // Verify user is admin
    const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
    if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
      throw new functions.https.HttpsError('permission-denied', 'Only admin users can verify PIN');
    }

    // Get stored PIN hash
    const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
    if (!securityDoc.exists || !securityDoc.data()?.adminPin) {
      throw new functions.https.HttpsError('not-found', 'Admin PIN not set. Please set up your PIN first.');
    }

    // Hash the provided PIN and compare
    const crypto = require('crypto');
    const hashedPin = crypto.createHash('sha256').update(pin).digest('hex');
    const storedPin = securityDoc.data()?.adminPin;

    if (hashedPin !== storedPin) {
      throw new functions.https.HttpsError('permission-denied', 'Invalid PIN');
    }

    // Update last access time
    await admin.firestore().collection('users').doc(context.auth.uid).update({
      lastAdminAccess: admin.firestore.Timestamp.now()
    });

    return {
      success: true,
      message: 'PIN verified successfully'
    };
  } catch (error) {
    console.error('Error verifying admin PIN:', error);
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', 'Failed to verify admin PIN');
  }
});

exports.setAdminPin = functions.https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { pin } = data;

    if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
      throw new functions.https.HttpsError('invalid-argument', 'PIN must be exactly 8 digits');
    }

    const admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }

    // Verify user is admin
    const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
    if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
      throw new functions.https.HttpsError('permission-denied', 'Only admin users can set PIN');
    }

    // Hash the PIN
    const crypto = require('crypto');
    const hashedPin = crypto.createHash('sha256').update(pin).digest('hex');

    // Store the hashed PIN
    await admin.firestore().collection('adminSettings').doc('security').set({
      adminPin: hashedPin,
      setBy: context.auth.uid,
      setAt: admin.firestore.Timestamp.now()
    }, { merge: true });

    return {
      success: true,
      message: 'Admin PIN set successfully'
    };
  } catch (error) {
    console.error('Error setting admin PIN:', error);
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', 'Failed to set admin PIN');
  }
});

exports.getWalletData = functions.https.onRequest(async (req, res) => {
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  try {
    if (req.method !== 'POST') {
      res.status(405).json({ error: 'Method not allowed' });
      return;
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    const admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(token);
    const userId = decodedToken.uid;

    const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();

    if (!walletDoc.exists) {
      const walletData = {
        userId,
        balance: 0,
        referralCode: `user${userId.substring(0, 6)}`,
        usedReferral: false,
        history: [],
        grantedBy: 'system',
        createdAt: admin.firestore.Timestamp.now(),
        lastUpdated: admin.firestore.Timestamp.now()
      };

      await admin.firestore().collection('wallets').doc(userId).set(walletData);
      res.status(200).json(walletData);
      return;
    }

    res.status(200).json(walletDoc.data());
  } catch (error) {
    console.error('Error getting wallet data:', error);
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
});

exports.getStripeConnectAccountStatus = functions.https.onRequest(async (req, res) => {
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  try {
    if (req.method !== 'POST') {
      res.status(405).json({ error: 'Method not allowed' });
      return;
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    const admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(token);
    const userId = decodedToken.uid;

    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    if (!userDoc.exists) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    const userData = userDoc.data();
    const stripeAccountId = userData?.stripeAccountId;

    if (!stripeAccountId) {
      res.status(200).json({
        hasAccount: false,
        needsOnboarding: true,
        canAcceptPayments: false
      });
      return;
    }

    res.status(200).json({
      hasAccount: true,
      needsOnboarding: false,
      canAcceptPayments: true,
      accountId: stripeAccountId
    });
  } catch (error) {
    console.error('Error getting Stripe Connect account status:', error);
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
});

exports.getSellerPendingPayouts = functions.https.onRequest(async (req, res) => {
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  try {
    if (req.method !== 'POST') {
      res.status(405).json({ error: 'Method not allowed' });
      return;
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    const admin = require('firebase-admin');
    if (!admin.apps.length) {
      admin.initializeApp();
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(token);
    const userId = decodedToken.uid;

    const ordersSnapshot = await admin.firestore()
      .collection('orders')
      .where('sellerId', '==', userId)
      .where('status', 'in', ['paid', 'shipped', 'pending_delivery'])
      .get();

    let totalPending = 0;
    const pendingOrders = [];

    ordersSnapshot.forEach(doc => {
      const order = doc.data();
      totalPending += order.sellerAmount || 0;
      pendingOrders.push({
        id: doc.id,
        ...order
      });
    });

    res.status(200).json({
      totalPending,
      pendingOrders,
      count: pendingOrders.length
    });
  } catch (error) {
    console.error('Error getting seller pending payouts:', error);
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
});
