"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.stripeWebhookError = exports.systemHealthCheck = exports.createSystemAlert = exports.createWarningNotification = exports.onOrderCreate = exports.onListingCreate = exports.onUserCreate = exports.createUserRecord = exports.createCheckoutSession = exports.searchListings = exports.createListing = exports.getListings = exports.testFunction = void 0;
// Minimal Firebase Functions for CORS Fix - Essential functions only
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin
if (!admin.apps.length) {
    admin.initializeApp();
}
// Helper function to verify authentication
const verifyAuth = async (context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    return context.auth;
};
// Helper function to handle errors
const handleError = (error) => {
    console.error('Function error:', error);
    if (error instanceof functions.https.HttpsError) {
        throw error;
    }
    const message = error instanceof Error ? error.message : 'Unknown error';
    throw new functions.https.HttpsError('internal', message);
};
// Test function with CORS
exports.testFunction = functions
    .runWith({
    memory: '128MB',
    timeoutSeconds: 30
})
    .https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
    }
    res.json({
        success: true,
        message: 'Hive Campus Functions deployed successfully - CORS Fixed',
        timestamp: new Date().toISOString(),
        version: '2.1.0'
    });
});
// Get listings function - ESSENTIAL for home page
exports.getListings = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        await verifyAuth(context);
        const { university, category, type, condition, minPrice, maxPrice, ownerId, status = 'active', limit = 20, lastVisible } = data;
        console.log('getListings called with data:', JSON.stringify(data, null, 2));
        // Get current user's university for visibility filtering
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        const currentUserUniversity = (_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.university;
        let query = admin.firestore().collection('listings')
            .where('status', '==', status)
            .orderBy('createdAt', 'desc');
        // Apply filters if provided
        if (university) {
            query = query.where('university', '==', university);
        }
        if (category) {
            query = query.where('category', '==', category);
        }
        if (type) {
            query = query.where('type', '==', type);
        }
        if (condition) {
            query = query.where('condition', '==', condition);
        }
        if (ownerId) {
            query = query.where('ownerId', '==', ownerId);
        }
        // Handle pagination
        if (lastVisible) {
            const lastDoc = await admin.firestore().collection('listings').doc(lastVisible).get();
            if (lastDoc.exists) {
                query = query.startAfter(lastDoc);
            }
        }
        // Apply limit
        query = query.limit(limit);
        // Execute query
        const snapshot = await query.get();
        // Process results
        const listings = [];
        let lastVisibleId = null;
        snapshot.forEach(doc => {
            const listing = doc.data();
            // Apply visibility filtering
            const isVisible = listing.visibility === 'public' ||
                (listing.visibility === 'university' && listing.university === currentUserUniversity);
            if (!isVisible) {
                return; // Skip this listing
            }
            // Apply price filtering in memory
            const price = listing.price;
            if ((minPrice === undefined || price >= minPrice) &&
                (maxPrice === undefined || price <= maxPrice)) {
                listings.push(Object.assign({ id: doc.id }, listing));
            }
            // Set the last visible document ID for pagination
            lastVisibleId = doc.id;
        });
        console.log(`getListings returning ${listings.length} listings`);
        return {
            success: true,
            data: {
                listings,
                lastVisible: lastVisibleId,
                total: listings.length
            }
        };
    }
    catch (error) {
        console.error('getListings error:', error);
        return handleError(error);
    }
});
// Create listing function - ESSENTIAL for adding listings
exports.createListing = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    try {
        const auth = await verifyAuth(context);
        const { title, description, price, category, condition, type, imageURLs } = data;
        console.log('createListing called with data:', JSON.stringify(data, null, 2));
        // Validate required fields
        if (!title || !description || price === undefined || !category || !condition || !type) {
            throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
        }
        // Get user data to include university
        const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User not found');
        }
        const userData = userDoc.data();
        // Create listing document
        const listingData = {
            title,
            description,
            price: parseFloat(price),
            category,
            condition,
            type,
            imageURLs: imageURLs || [],
            ownerId: auth.uid,
            ownerName: userData.name || 'Unknown',
            ownerEmail: userData.email,
            university: userData.university,
            status: 'active',
            visibility: 'university',
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        // Add listing to Firestore
        const docRef = await admin.firestore().collection('listings').add(listingData);
        console.log('createListing created listing with ID:', docRef.id);
        return {
            success: true,
            data: Object.assign({ id: docRef.id }, listingData)
        };
    }
    catch (error) {
        console.error('createListing error:', error);
        return handleError(error);
    }
});
// Search listings function - ESSENTIAL for search
exports.searchListings = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        await verifyAuth(context);
        const { query, limit = 20 } = data;
        console.log('searchListings called with query:', query);
        if (!query || typeof query !== 'string') {
            throw new functions.https.HttpsError('invalid-argument', 'Search query is required');
        }
        // Get current user's university for visibility filtering
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        const currentUserUniversity = (_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.university;
        // Simple text search in title and description
        const searchTerms = query.toLowerCase().trim().split(/\s+/);
        // Get all active listings
        const snapshot = await admin.firestore()
            .collection('listings')
            .where('status', '==', 'active')
            .limit(100) // Limit for performance
            .get();
        const results = [];
        snapshot.forEach(doc => {
            const listing = doc.data();
            // Apply visibility filtering
            const isVisible = listing.visibility === 'public' ||
                (listing.visibility === 'university' && listing.university === currentUserUniversity);
            if (!isVisible) {
                return;
            }
            // Check if any search term matches title or description
            const titleLower = (listing.title || '').toLowerCase();
            const descriptionLower = (listing.description || '').toLowerCase();
            const matches = searchTerms.some(term => titleLower.includes(term) || descriptionLower.includes(term));
            if (matches) {
                results.push(Object.assign({ id: doc.id }, listing));
            }
        });
        // Sort by relevance (simple: by title match first)
        results.sort((a, b) => {
            const aTitle = (a.title || '').toLowerCase();
            const bTitle = (b.title || '').toLowerCase();
            const queryLower = query.toLowerCase();
            const aTitleMatch = aTitle.includes(queryLower);
            const bTitleMatch = bTitle.includes(queryLower);
            if (aTitleMatch && !bTitleMatch)
                return -1;
            if (!aTitleMatch && bTitleMatch)
                return 1;
            return 0;
        });
        console.log(`searchListings returning ${results.length} results`);
        return {
            success: true,
            data: {
                listings: results.slice(0, limit),
                total: results.length
            }
        };
    }
    catch (error) {
        console.error('searchListings error:', error);
        return handleError(error);
    }
});
// Simple checkout session creation - ESSENTIAL for Buy Now
exports.createCheckoutSession = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        const auth = await verifyAuth(context);
        const { listingId, useWalletBalance } = data;
        console.log('createCheckoutSession called with:', { listingId, useWalletBalance });
        if (!listingId) {
            throw new functions.https.HttpsError('invalid-argument', 'Listing ID is required');
        }
        // Get listing details
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Listing not found');
        }
        const listing = listingDoc.data();
        // Check if user is trying to buy their own listing
        if (listing.ownerId === auth.uid) {
            throw new functions.https.HttpsError('invalid-argument', 'Cannot purchase your own listing');
        }
        // Create order document
        const orderData = {
            listingId,
            buyerId: auth.uid,
            sellerId: listing.ownerId,
            amount: listing.price,
            status: 'pending_payment',
            listingTitle: listing.title,
            listingImage: ((_a = listing.imageURLs) === null || _a === void 0 ? void 0 : _a[0]) || null,
            category: listing.category,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        const orderRef = await admin.firestore().collection('orders').add(orderData);
        console.log('createCheckoutSession created order:', orderRef.id);
        // For now, return a mock checkout session URL
        // In production, this would create a real Stripe checkout session
        const mockSessionUrl = `https://checkout.stripe.com/pay/mock-session-${orderRef.id}`;
        return {
            success: true,
            sessionUrl: mockSessionUrl,
            orderId: orderRef.id
        };
    }
    catch (error) {
        console.error('createCheckoutSession error:', error);
        return handleError(error);
    }
});
// User creation function - ESSENTIAL for auth
exports.createUserRecord = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .auth.user().onCreate(async (user) => {
    try {
        const { uid, email, displayName } = user;
        console.log('createUserRecord called for:', email);
        if (!email) {
            throw new Error('User email is required');
        }
        // Verify educational email
        const isEducationalEmail = (email) => {
            return email.endsWith('.edu') ||
                email.includes('@outlook.') ||
                email.includes('@hotmail.') ||
                email.includes('@live.') ||
                email.includes('@student.');
        };
        if (!isEducationalEmail(email)) {
            await admin.auth().deleteUser(uid);
            throw new Error('Only .edu email addresses are allowed to register');
        }
        // Extract university from email domain
        const emailParts = email.split('@');
        const domain = emailParts[1];
        let university = domain.split('.')[0];
        university = university.charAt(0).toUpperCase() + university.slice(1);
        // Determine role based on email
        const isAdminEmail = email === '<EMAIL>';
        const userRole = isAdminEmail ? 'admin' : 'student';
        // Create user document
        const userData = {
            uid,
            name: displayName || email.split('@')[0],
            email,
            role: userRole,
            university,
            emailVerified: true,
            status: 'active',
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        await admin.firestore().collection('users').doc(uid).set(userData);
        // Set custom claims for admin users
        if (isAdminEmail) {
            await admin.auth().setCustomUserClaims(uid, { role: 'admin' });
            console.log('Admin role set for user:', email);
        }
        else {
            await admin.auth().setCustomUserClaims(uid, { role: 'student' });
        }
        console.log('createUserRecord completed for:', email);
        return { success: true };
    }
    catch (error) {
        console.error('Error creating user record:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return { success: false, error: errorMessage };
    }
});
// Export admin notification functions
var adminNotifications_1 = require("./adminNotifications");
Object.defineProperty(exports, "onUserCreate", { enumerable: true, get: function () { return adminNotifications_1.onUserCreate; } });
Object.defineProperty(exports, "onListingCreate", { enumerable: true, get: function () { return adminNotifications_1.onListingCreate; } });
Object.defineProperty(exports, "onOrderCreate", { enumerable: true, get: function () { return adminNotifications_1.onOrderCreate; } });
Object.defineProperty(exports, "createWarningNotification", { enumerable: true, get: function () { return adminNotifications_1.createWarningNotification; } });
Object.defineProperty(exports, "createSystemAlert", { enumerable: true, get: function () { return adminNotifications_1.createSystemAlert; } });
Object.defineProperty(exports, "systemHealthCheck", { enumerable: true, get: function () { return adminNotifications_1.systemHealthCheck; } });
Object.defineProperty(exports, "stripeWebhookError", { enumerable: true, get: function () { return adminNotifications_1.stripeWebhookError; } });
//# sourceMappingURL=index-cors-fix.js.map