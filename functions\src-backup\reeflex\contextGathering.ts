import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { CallableContext } from 'firebase-functions/v1/https';

// Initialize Firebase Admin if not already done
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

interface SystemContext {
  recentErrors: any[];
  performanceMetrics: any;
  firebaseData: any;
  appMetrics: any;
  systemHealth: any;
  userFeedback: any[];
}

// Function to gather comprehensive system context
export const gatherSystemContext = functions.https.onCall(async (data, context: CallableContext) => {
  // Verify admin authentication
  if (!context.auth || !context.auth.token.admin) {
    throw new functions.https.HttpsError('permission-denied', 'Admin access required');
  }

  try {
    const systemContext: SystemContext = {
      recentErrors: await getRecentErrors(),
      performanceMetrics: await getPerformanceMetrics(),
      firebaseData: await getFirebaseMetrics(),
      appMetrics: await getAppMetrics(),
      systemHealth: await getSystemHealth(),
      userFeedback: await getUserFeedback()
    };

    return systemContext;
  } catch (error) {
    functions.logger.error('Error gathering system context:', error);
    throw new functions.https.HttpsError('internal', 'Failed to gather system context');
  }
});

async function getRecentErrors(limitCount = 10): Promise<any[]> {
  try {
    const errorsSnapshot = await db.collection('reeflex_activity')
      .where('type', '==', 'error')
      .orderBy('timestamp', 'desc')
      .limit(limitCount)
      .get();
    
    return errorsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    functions.logger.error('Error fetching recent errors:', error);
    return [];
  }
}

async function getPerformanceMetrics(): Promise<any> {
  try {
    const performanceSnapshot = await db.collection('reeflex_activity')
      .where('type', '==', 'performance')
      .orderBy('timestamp', 'desc')
      .limit(10)
      .get();
    
    const performanceData = performanceSnapshot.docs.map(doc => doc.data());
    
    // Calculate performance statistics
    const slowOperations = performanceData.filter(p => p.data?.duration > 3000);
    const avgResponseTime = performanceData.reduce((sum, p) => sum + (p.data?.duration || 0), 0) / performanceData.length;
    
    return {
      totalOperations: performanceData.length,
      slowOperations: slowOperations.length,
      averageResponseTime: avgResponseTime,
      recentPerformanceData: performanceData.slice(0, 5)
    };
  } catch (error) {
    functions.logger.error('Error fetching performance metrics:', error);
    return null;
  }
}

async function getFirebaseMetrics(): Promise<any> {
  try {
    // Get user metrics
    const usersSnapshot = await db.collection('users').get();
    const totalUsers = usersSnapshot.size;
    
    // Get recent signups (last 7 days)
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    
    const recentSignupsSnapshot = await db.collection('users')
      .where('createdAt', '>=', admin.firestore.Timestamp.fromDate(weekAgo))
      .get();
    const newSignups = recentSignupsSnapshot.size;

    // Get error count from ReeFlex activity
    const errorsSnapshot = await db.collection('reeflex_activity')
      .where('type', '==', 'error')
      .where('timestamp', '>=', admin.firestore.Timestamp.fromDate(weekAgo))
      .get();
    const errorCount = errorsSnapshot.size;

    return {
      auth: {
        totalUsers,
        activeUsers: Math.floor(totalUsers * 0.7), // Estimate
        newSignups,
        failedLogins: 0 // Would need to track this separately
      },
      firestore: {
        reads: 0, // Would need Firebase Analytics
        writes: 0,
        deletes: 0,
        errors: errorCount
      },
      storage: {
        uploads: 0, // Would need to track this
        downloads: 0,
        errors: 0
      },
      functions: {
        invocations: 0, // Would need Firebase Analytics
        errors: 0,
        duration: 0
      }
    };
  } catch (error) {
    functions.logger.error('Error getting Firebase metrics:', error);
    return {
      auth: { totalUsers: 0, activeUsers: 0, newSignups: 0, failedLogins: 0 },
      firestore: { reads: 0, writes: 0, deletes: 0, errors: 0 },
      storage: { uploads: 0, downloads: 0, errors: 0 },
      functions: { invocations: 0, errors: 0, duration: 0 }
    };
  }
}

async function getAppMetrics(): Promise<any> {
  try {
    // Get listings metrics
    const listingsSnapshot = await db.collection('listings').get();
    const allListings = listingsSnapshot.docs.map(doc => doc.data());
    
    const activeListings = allListings.filter(listing => listing.status === 'active').length;
    const soldListings = allListings.filter(listing => listing.status === 'sold').length;

    // Get messages metrics
    const messagesSnapshot = await db.collection('chats').get();
    const totalMessages = messagesSnapshot.size;

    // Get orders metrics (if orders collection exists)
    let ordersMetrics = {
      total: 0,
      pending: 0,
      completed: 0,
      cancelled: 0
    };

    try {
      const ordersSnapshot = await db.collection('orders').get();
      const allOrders = ordersSnapshot.docs.map(doc => doc.data());
      
      ordersMetrics = {
        total: allOrders.length,
        pending: allOrders.filter(order => order.status === 'pending').length,
        completed: allOrders.filter(order => order.status === 'completed').length,
        cancelled: allOrders.filter(order => order.status === 'cancelled').length
      };
    } catch (error) {
      // Orders collection might not exist yet
      functions.logger.info('Orders collection not found, using default values');
    }

    return {
      listings: {
        total: allListings.length,
        active: activeListings,
        sold: soldListings,
        views: 0 // Would need to track this separately
      },
      messages: {
        total: totalMessages,
        unread: 0 // Would need to track this separately
      },
      orders: ordersMetrics
    };
  } catch (error) {
    functions.logger.error('Error getting app metrics:', error);
    return {
      listings: { total: 0, active: 0, sold: 0, views: 0 },
      messages: { total: 0, unread: 0 },
      orders: { total: 0, pending: 0, completed: 0, cancelled: 0 }
    };
  }
}

async function getSystemHealth(): Promise<any> {
  try {
    const issues: string[] = [];
    
    // Check for recent errors
    const recentErrors = await getRecentErrors(5);
    if (recentErrors.length > 3) {
      issues.push(`High error rate: ${recentErrors.length} errors in recent activity`);
    }

    // Check for performance issues
    const performanceMetrics = await getPerformanceMetrics();
    if (performanceMetrics && performanceMetrics.slowOperations > 0) {
      issues.push(`Performance issues detected: ${performanceMetrics.slowOperations} slow operations`);
    }

    // Determine overall status
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (issues.length > 0) {
      status = issues.length > 2 ? 'critical' : 'warning';
    }

    return {
      status,
      issues,
      uptime: Date.now() - new Date('2024-01-01').getTime(), // Placeholder uptime
      lastChecked: new Date().toISOString()
    };
  } catch (error) {
    functions.logger.error('Error getting system health:', error);
    return {
      status: 'critical',
      issues: ['Unable to determine system health'],
      uptime: 0,
      lastChecked: new Date().toISOString()
    };
  }
}

async function getUserFeedback(limitCount = 10): Promise<any[]> {
  try {
    const feedbackSnapshot = await db.collection('feedback')
      .orderBy('createdAt', 'desc')
      .limit(limitCount)
      .get();
    
    return feedbackSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    functions.logger.error('Error fetching user feedback:', error);
    return [];
  }
}

// Function to log ReeFlex insights
export const logReeflexInsight = functions.https.onCall(async (data, context: CallableContext) => {
  // Verify admin authentication
  if (!context.auth || !context.auth.token.admin) {
    throw new functions.https.HttpsError('permission-denied', 'Admin access required');
  }

  const { type, severity, title, description, recommendation, confidence, relatedData } = data;

  if (!type || !severity || !title || !description) {
    throw new functions.https.HttpsError('invalid-argument', 'Required fields missing');
  }

  try {
    await db.collection('reeflex_insights').add({
      type,
      severity,
      title,
      description,
      recommendation: recommendation || '',
      confidence: confidence || 0.5,
      relatedData: relatedData || {},
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      adminId: context.auth.uid
    });

    return { success: true };
  } catch (error) {
    functions.logger.error('Error logging ReeFlex insight:', error);
    throw new functions.https.HttpsError('internal', 'Failed to log insight');
  }
});
