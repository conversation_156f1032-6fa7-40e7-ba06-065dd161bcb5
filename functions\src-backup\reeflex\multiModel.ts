import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { CallableContext } from 'firebase-functions/v1/https';
import axios from 'axios';

// Initialize Firebase Admin if not already done
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

interface MultiModelRequest {
  prompt: string;
  systemPrompt?: string;
  models: string[];
  temperature?: number;
  maxTokens?: number;
  adminId: string;
}

interface ModelResponse {
  id: string;
  model: string;
  content: string;
  latency: number;
  tokens?: {
    prompt: number;
    completion: number;
    total: number;
  };
  error?: string;
  timestamp: Date;
}

interface MultiModelResponse {
  responses: ModelResponse[];
  verdict: string;
  requestId: string;
  timestamp: Date;
  totalLatency: number;
}

// OpenAI Service
class OpenAIService {
  private apiKey: string;
  private baseURL = 'https://api.openai.com/v1';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateResponse(
    prompt: string,
    systemPrompt?: string,
    temperature = 0.7,
    maxTokens = 2000
  ): Promise<ModelResponse> {
    const startTime = Date.now();
    
    try {
      const messages: Array<{role: string, content: string}> = [];

      if (systemPrompt) {
        messages.push({ role: 'system', content: systemPrompt });
      }

      messages.push({ role: 'user', content: prompt });

      const response = await axios.post(
        `${this.baseURL}/chat/completions`,
        {
          model: 'gpt-4',
          messages,
          temperature,
          max_tokens: maxTokens
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          timeout: 30000
        }
      );

      const latency = Date.now() - startTime;
      
      return {
        id: response.data.id,
        model: response.data.model,
        content: response.data.choices[0].message.content,
        latency,
        tokens: {
          prompt: response.data.usage.prompt_tokens,
          completion: response.data.usage.completion_tokens,
          total: response.data.usage.total_tokens
        },
        timestamp: new Date()
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        id: 'error',
        model: 'gpt-4',
        content: '',
        latency,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      };
    }
  }
}

// Gemini Service
class GeminiService {
  private apiKey: string;
  private baseURL = 'https://generativelanguage.googleapis.com/v1beta';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateResponse(
    prompt: string,
    systemPrompt?: string,
    temperature = 0.7,
    maxTokens = 2000
  ): Promise<ModelResponse> {
    const startTime = Date.now();
    
    try {
      const fullPrompt = systemPrompt 
        ? `${systemPrompt}\n\nUser: ${prompt}`
        : prompt;

      const response = await axios.post(
        `${this.baseURL}/models/gemini-pro:generateContent?key=${this.apiKey}`,
        {
          contents: [{
            parts: [{ text: fullPrompt }]
          }],
          generationConfig: {
            temperature,
            maxOutputTokens: maxTokens,
            topP: 0.8,
            topK: 10
          }
        },
        {
          headers: { 'Content-Type': 'application/json' },
          timeout: 30000
        }
      );

      const latency = Date.now() - startTime;
      const content = response.data.candidates[0]?.content?.parts[0]?.text || '';
      
      return {
        id: `gemini-${Date.now()}`,
        model: 'gemini-pro',
        content,
        latency,
        tokens: {
          prompt: response.data.usageMetadata?.promptTokenCount || 0,
          completion: response.data.usageMetadata?.candidatesTokenCount || 0,
          total: response.data.usageMetadata?.totalTokenCount || 0
        },
        timestamp: new Date()
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        id: 'error',
        model: 'gemini-pro',
        content: '',
        latency,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      };
    }
  }
}

// OpenRouter Service
class OpenRouterService {
  private apiKey: string;
  private baseURL = 'https://openrouter.ai/api/v1';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateResponse(
    prompt: string,
    systemPrompt?: string,
    temperature = 0.7,
    maxTokens = 2000
  ): Promise<ModelResponse> {
    const startTime = Date.now();
    
    try {
      const messages: Array<{role: string, content: string}> = [];

      if (systemPrompt) {
        messages.push({ role: 'system', content: systemPrompt });
      }

      messages.push({ role: 'user', content: prompt });

      const response = await axios.post(
        `${this.baseURL}/chat/completions`,
        {
          model: 'anthropic/claude-3-haiku', // Default to Claude 3 Haiku
          messages,
          temperature,
          max_tokens: maxTokens,
          route: 'fallback'
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
            'HTTP-Referer': 'https://hivecampus.app',
            'X-Title': 'Hive Campus ReeFlex AI'
          },
          timeout: 30000
        }
      );

      const latency = Date.now() - startTime;
      
      return {
        id: response.data.id,
        model: response.data.model,
        content: response.data.choices[0].message.content,
        latency,
        tokens: {
          prompt: response.data.usage.prompt_tokens,
          completion: response.data.usage.completion_tokens,
          total: response.data.usage.total_tokens
        },
        timestamp: new Date()
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        id: 'error',
        model: 'deepseek-chat',
        content: '',
        latency,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      };
    }
  }
}

// Main ReeFlex Multi-Model Function
export const reeflexMultiModel = functions.https.onCall(async (data: MultiModelRequest, context: CallableContext) => {
  // Verify admin authentication
  if (!context.auth || !context.auth.token.admin) {
    throw new functions.https.HttpsError('permission-denied', 'Admin access required');
  }

  const { prompt, systemPrompt, models, temperature = 0.7, maxTokens = 2000, adminId } = data;
  
  if (!prompt || !models || models.length === 0) {
    throw new functions.https.HttpsError('invalid-argument', 'Prompt and models are required');
  }

  const startTime = Date.now();
  const requestId = `reeflex-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    // Initialize services
    const services: { [key: string]: any } = {};

    // Get API keys from Firebase Functions config or environment variables
    const openaiKey = functions.config().openai?.api_key || process.env.OPENAI_API_KEY;
    const geminiKey = functions.config().gemini?.api_key || process.env.GEMINI_API_KEY;
    const openrouterKey = functions.config().openrouter?.api_key || process.env.OPENROUTER_API_KEY;

    if (openaiKey) {
      services.openai = new OpenAIService(openaiKey);
    }

    if (geminiKey) {
      services.gemini = new GeminiService(geminiKey);
    }

    if (openrouterKey) {
      services.openrouter = new OpenRouterService(openrouterKey);
    }

    // Enhanced system prompt for ReeFlex
    const reeflexSystemPrompt = `You are ReeFlex — a multi-agent embedded AI engineering system that lives silently inside the Hive Campus codebase. You are not one model, but part of a team of three AI models (OpenAI, Google Gemini, and DeepSeek) working together as a senior AI engineering team.

Your role is to constantly observe, diagnose, and improve Hive Campus - a student marketplace application built with React, TypeScript, Firebase, and Stripe.

You have access to real-time data about:
- Firebase Authentication, Firestore, Storage, and Functions
- Stripe payments, escrow, and Connect integration
- User behavior, errors, and performance metrics
- Shipping integration with Shippo
- Real-time chat and messaging systems
- Admin dashboard and analytics

When responding:
1. Be technical but accessible
2. Provide actionable insights
3. Consider the full stack (frontend, backend, payments, shipping)
4. Think like a senior engineer who understands both code and business
5. Prioritize user experience and platform stability
6. Consider scalability and security implications

${systemPrompt || ''}`;

    // Process requests in parallel
    const promises: Promise<ModelResponse>[] = [];
    
    for (const model of models) {
      if (services[model]) {
        promises.push(
          services[model].generateResponse(prompt, reeflexSystemPrompt, temperature, maxTokens)
        );
      }
    }

    // Wait for all responses
    const responses = await Promise.all(promises);
    
    // Generate verdict
    const verdict = generateVerdict(responses, prompt);
    
    const totalLatency = Date.now() - startTime;
    
    const result: MultiModelResponse = {
      responses,
      verdict,
      requestId,
      timestamp: new Date(),
      totalLatency
    };

    // Log conversation to Firestore
    await db.collection('reeflex_logs').add({
      adminId,
      requestId,
      prompt,
      responses,
      verdict,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      latency: totalLatency
    });

    return result;
  } catch (error) {
    functions.logger.error('Error in reeflexMultiModel:', error);
    throw new functions.https.HttpsError('internal', 'Failed to process multi-model request');
  }
});

function generateVerdict(responses: ModelResponse[], originalPrompt: string): string {
  const validResponses = responses.filter(r => !r.error && r.content.trim().length > 0);
  
  if (validResponses.length === 0) {
    return "❌ All models failed to generate responses. Please check API configurations and try again.";
  }

  if (validResponses.length === 1) {
    return `🧠 **ReeFlex Verdict** (Single Model Response)\n\n${validResponses[0].content}`;
  }

  // Simple synthesis logic
  const uniqueInsights: string[] = [];
  
  validResponses.forEach((response, index) => {
    const modelName = response.model.includes('gpt') ? 'OpenAI' : 
                     response.model.includes('gemini') ? 'Gemini' : 'DeepSeek';
    
    uniqueInsights.push(`**${modelName}**: ${response.content.substring(0, 200)}...`);
  });

  return `🧠 **ReeFlex Verdict** (Synthesized from ${validResponses.length} models)\n\nBased on analysis from ${validResponses.length} AI models:\n\n${uniqueInsights.join('\n\n')}\n\n**Consensus**: The models agree on the need for immediate attention to the issues raised. Consider implementing the most frequently mentioned recommendations first.`;
}
