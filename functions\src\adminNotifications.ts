import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin if not already done
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

/**
 * Create an admin notification
 */
async function createAdminNotification(
  type: string,
  message: string,
  title?: string,
  metadata?: Record<string, any>
): Promise<void> {
  try {
    await db.collection('adminNotifications').add({
      type,
      message,
      title: title || getDefaultTitle(type),
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      read: false,
      metadata: metadata || {}
    });
  } catch (error) {
    console.error('Error creating admin notification:', error);
  }
}

/**
 * Get default title for notification type
 */
function getDefaultTitle(type: string): string {
  const titles: Record<string, string> = {
    new_user: '👤 New User Registration',
    new_listing: '📦 New Listing Created',
    purchase: '💰 Purchase Completed',
    warning: '⚠️ System Warning',
    error: '❌ System Error',
    stripe_webhook: '💳 Stripe Webhook Issue',
    payment_failed: '💸 Payment Failed',
    suspicious_activity: '🚨 Suspicious Activity',
    listing_flagged: '🚩 Listing Flagged',
    user_blocked: '🚫 User Action Required',
    system_alert: '🔔 System Alert',
    maintenance: '🔧 Maintenance Notice',
    security: '🔒 Security Alert',
    performance: '⚡ Performance Issue'
  };
  return titles[type] || '📢 Admin Notification';
}

/**
 * Firebase Auth trigger - New user registration
 */
export const onUserCreate = functions.auth.user().onCreate(async (user) => {
  try {
    // Get user data from Firestore if available
    let userData: any = {};
    try {
      const userDoc = await db.collection('users').doc(user.uid).get();
      userData = userDoc.data() || {};
    } catch (error) {
      console.log('User document not found in Firestore yet');
    }

    await createAdminNotification(
      'new_user',
      `New user registered: ${user.email}${userData.university ? ` from ${userData.university}` : ''}`,
      undefined,
      {
        userId: user.uid,
        userName: user.displayName || userData.name || 'Unknown',
        userEmail: user.email,
        university: userData.university,
        emailVerified: user.emailVerified,
        severity: 'low'
      }
    );
  } catch (error) {
    console.error('Error in onUserCreate notification:', error);
  }
});

/**
 * Firestore trigger - New listing created
 */
export const onListingCreate = functions.firestore
  .document('listings/{listingId}')
  .onCreate(async (snap, context) => {
    try {
      const listing = snap.data();
      const listingId = context.params.listingId;

      // Get user data
      let userName = 'Unknown User';
      try {
        const userDoc = await db.collection('users').doc(listing.ownerId).get();
        const userData = userDoc.data();
        userName = userData?.name || 'Unknown User';
      } catch (error) {
        console.log('Could not fetch user data for listing owner');
      }

      await createAdminNotification(
        'new_listing',
        `New ${listing.type} listing: "${listing.title}" by ${userName} - $${listing.price}`,
        undefined,
        {
          listingId,
          listingTitle: listing.title,
          userId: listing.ownerId,
          userName,
          price: listing.price,
          category: listing.category,
          university: listing.university,
          severity: 'low'
        }
      );
    } catch (error) {
      console.error('Error in onListingCreate notification:', error);
    }
  });

/**
 * Firestore trigger - Order/Purchase created
 */
export const onOrderCreate = functions.firestore
  .document('orders/{orderId}')
  .onCreate(async (snap, context) => {
    try {
      const order = snap.data();
      const orderId = context.params.orderId;

      // Get buyer and seller data
      let buyerName = 'Unknown Buyer';
      let sellerName = 'Unknown Seller';
      
      try {
        const [buyerDoc, sellerDoc] = await Promise.all([
          db.collection('users').doc(order.buyerId).get(),
          db.collection('users').doc(order.sellerId).get()
        ]);
        
        buyerName = buyerDoc.data()?.name || 'Unknown Buyer';
        sellerName = sellerDoc.data()?.name || 'Unknown Seller';
      } catch (error) {
        console.log('Could not fetch user data for order');
      }

      await createAdminNotification(
        'purchase',
        `New purchase: ${buyerName} bought "${order.itemTitle}" from ${sellerName} for $${order.totalAmount}`,
        undefined,
        {
          orderId,
          buyerId: order.buyerId,
          sellerId: order.sellerId,
          buyerName,
          sellerName,
          itemTitle: order.itemTitle,
          orderAmount: order.totalAmount,
          paymentStatus: order.paymentStatus,
          severity: 'medium'
        }
      );
    } catch (error) {
      console.error('Error in onOrderCreate notification:', error);
    }
  });

/**
 * Callable function - Create warning notification
 */
export const createWarningNotification = functions.https.onCall(async (data, context) => {
  // Verify admin authentication
  if (!context.auth || !context.auth.token.admin) {
    throw new functions.https.HttpsError('permission-denied', 'Admin access required');
  }

  const { message, severity = 'medium', metadata = {} } = data;

  if (!message) {
    throw new functions.https.HttpsError('invalid-argument', 'Message is required');
  }

  try {
    await createAdminNotification(
      'warning',
      message,
      undefined,
      {
        ...metadata,
        severity,
        createdBy: context.auth.uid
      }
    );

    return { success: true };
  } catch (error) {
    console.error('Error creating warning notification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create notification');
  }
});

/**
 * Callable function - Create system alert notification
 */
export const createSystemAlert = functions.https.onCall(async (data, context) => {
  // Allow system calls (no auth required for internal system alerts)
  const { type, message, title, metadata = {} } = data;

  if (!type || !message) {
    throw new functions.https.HttpsError('invalid-argument', 'Type and message are required');
  }

  try {
    await createAdminNotification(type, message, title, metadata);
    return { success: true };
  } catch (error) {
    console.error('Error creating system alert:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create notification');
  }
});

/**
 * Scheduled function - Check for system issues and create alerts
 */
export const systemHealthCheck = functions.pubsub
  .schedule('every 30 minutes')
  .onRun(async (context) => {
    try {
      // Check for failed orders in the last hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const failedOrdersQuery = await db.collection('orders')
        .where('paymentStatus', '==', 'failed')
        .where('createdAt', '>=', oneHourAgo)
        .get();

      if (failedOrdersQuery.size > 5) {
        await createAdminNotification(
          'warning',
          `High number of failed payments detected: ${failedOrdersQuery.size} failed orders in the last hour`,
          undefined,
          {
            failedOrderCount: failedOrdersQuery.size,
            timeframe: '1 hour',
            severity: 'high'
          }
        );
      }

      // Check for suspicious activity (multiple failed logins, etc.)
      // This would require additional tracking in your auth system

      console.log('System health check completed');
    } catch (error) {
      console.error('Error in system health check:', error);
      
      await createAdminNotification(
        'error',
        'System health check failed to complete',
        undefined,
        {
          error: error instanceof Error ? error.message : String(error),
          severity: 'medium'
        }
      );
    }
  });

/**
 * HTTP function - Stripe webhook error handler
 */
export const stripeWebhookError = functions.https.onRequest(async (req, res) => {
  try {
    const { error, webhookId, eventType } = req.body;

    await createAdminNotification(
      'stripe_webhook',
      `Stripe webhook error: ${error} for event ${eventType}`,
      undefined,
      {
        webhookId,
        eventType,
        error,
        severity: 'high'
      }
    );

    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error handling stripe webhook error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Utility function to create notifications from other functions
 */
export const notifyAdmin = async (
  type: string,
  message: string,
  title?: string,
  metadata?: Record<string, any>
): Promise<void> => {
  return createAdminNotification(type, message, title, metadata);
};
