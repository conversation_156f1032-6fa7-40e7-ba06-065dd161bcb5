// Clean Firebase Functions for Hive Campus - Essential functions only
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp();
}

// Helper function to verify authentication
const verifyAuth = async (context: functions.https.CallableContext) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  return context.auth;
};

// Helper function to handle errors
const handleError = (error: any) => {
  console.error('Function error:', error);
  if (error instanceof functions.https.HttpsError) {
    throw error;
  }
  const message = error instanceof Error ? error.message : 'Unknown error';
  throw new functions.https.HttpsError('internal', message);
};

// Test function
export const testFunction = functions
  .runWith({
    memory: '128MB',
    timeoutSeconds: 30
  })
  .https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }
    
    res.json({
      success: true,
      message: 'Hive Campus Functions deployed successfully',
      timestamp: new Date().toISOString(),
      version: '3.0.0-CLEAN'
    });
  });

// Get a single listing by ID
export const getListingById = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30
  })
  .https.onCall(async (data, context) => {
    try {
      await verifyAuth(context);
      
      const { listingId } = data;
      
      if (!listingId) {
        throw new functions.https.HttpsError('invalid-argument', 'Listing ID is required');
      }

      const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
      
      if (!listingDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Listing not found');
      }

      const listing = listingDoc.data();

      // Increment view count
      await admin.firestore().collection('listings').doc(listingId).update({
        views: admin.firestore.FieldValue.increment(1)
      });

      return {
        success: true,
        data: {
          id: listingId,
          ...listing
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Get listings function - ESSENTIAL for home page
export const getListings = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      await verifyAuth(context);

      const {
        university,
        category,
        type,
        condition,
        minPrice,
        maxPrice,
        ownerId,
        status = 'active',
        limit = 20,
        lastVisible
      } = data;

      console.log('getListings called with data:', JSON.stringify(data, null, 2));

      // Get current user's university for visibility filtering
      const userDoc = await admin.firestore().collection('users').doc(context.auth!.uid).get();
      const currentUserUniversity = userDoc.data()?.university;

      let query = admin.firestore().collection('listings')
        .where('status', '==', status)
        .orderBy('createdAt', 'desc');

      // Apply filters if provided
      if (university) {
        query = query.where('university', '==', university);
      }

      if (category) {
        query = query.where('category', '==', category);
      }

      if (type) {
        query = query.where('type', '==', type);
      }

      if (condition) {
        query = query.where('condition', '==', condition);
      }

      if (ownerId) {
        query = query.where('ownerId', '==', ownerId);
      }
      
      // Handle pagination
      if (lastVisible) {
        const lastDoc = await admin.firestore().collection('listings').doc(lastVisible).get();
        if (lastDoc.exists) {
          query = query.startAfter(lastDoc);
        }
      }
      
      // Apply limit
      query = query.limit(limit);
      
      // Execute query
      const snapshot = await query.get();
      
      // Process results
      const listings: any[] = [];
      let lastVisibleId: string | null = null;
      
      snapshot.forEach(doc => {
        const listing = doc.data();

        // Apply visibility filtering
        const isVisible = listing.visibility === 'public' ||
                         (listing.visibility === 'university' && listing.university === currentUserUniversity);

        if (!isVisible) {
          return; // Skip this listing
        }

        // Apply price filtering in memory
        const price = listing.price;
        if ((minPrice === undefined || price >= minPrice) &&
            (maxPrice === undefined || price <= maxPrice)) {
          listings.push({
            id: doc.id,
            ...listing
          });
        }

        // Set the last visible document ID for pagination
        lastVisibleId = doc.id;
      });
      
      console.log(`getListings returning ${listings.length} listings`);
      
      return {
        success: true,
        data: {
          listings,
          lastVisible: lastVisibleId,
          total: listings.length
        }
      };
    } catch (error) {
      console.error('getListings error:', error);
      return handleError(error);
    }
  });

// Create listing function - ESSENTIAL for adding listings
export const createListing = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      const auth = await verifyAuth(context);

      const {
        title,
        description,
        price,
        category,
        condition,
        type,
        imageURLs
      } = data;
      
      console.log('createListing called with data:', JSON.stringify(data, null, 2));
      
      // Validate required fields
      if (!title || !description || price === undefined || !category || !condition || !type) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Missing required fields'
        );
      }
      
      // Get user data to include university
      const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();

      if (!userDoc.exists) {
        throw new functions.https.HttpsError(
          'not-found',
          'User not found'
        );
      }

      const userData = userDoc.data()!;
      
      // Create listing document
      const listingData = {
        title,
        description,
        price: parseFloat(price),
        category,
        condition,
        type,
        imageURLs: imageURLs || [],
        ownerId: auth.uid,
        ownerName: userData.name || 'Unknown',
        ownerEmail: userData.email,
        university: userData.university,
        status: 'active',
        visibility: 'university',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      };
      
      // Add listing to Firestore
      const docRef = await admin.firestore().collection('listings').add(listingData);
      
      console.log('createListing created listing with ID:', docRef.id);
      
      return {
        success: true,
        data: {
          id: docRef.id,
          ...listingData
        }
      };
    } catch (error) {
      console.error('createListing error:', error);
      return handleError(error);
    }
  });

// Search listings function - ESSENTIAL for search
export const searchListings = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      await verifyAuth(context);
      
      const { query, limit = 20 } = data;
      
      console.log('searchListings called with query:', query);
      
      if (!query || typeof query !== 'string') {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Search query is required'
        );
      }
      
      // Get current user's university for visibility filtering
      const userDoc = await admin.firestore().collection('users').doc(context.auth!.uid).get();
      const currentUserUniversity = userDoc.data()?.university;
      
      // Simple text search in title and description
      const searchTerms = query.toLowerCase().trim().split(/\s+/);
      
      // Get all active listings
      const snapshot = await admin.firestore()
        .collection('listings')
        .where('status', '==', 'active')
        .limit(100) // Limit for performance
        .get();
      
      const results: any[] = [];
      
      snapshot.forEach(doc => {
        const listing = doc.data();
        
        // Apply visibility filtering
        const isVisible = listing.visibility === 'public' ||
                         (listing.visibility === 'university' && listing.university === currentUserUniversity);
        
        if (!isVisible) {
          return;
        }
        
        // Check if any search term matches title or description
        const titleLower = (listing.title || '').toLowerCase();
        const descriptionLower = (listing.description || '').toLowerCase();
        
        const matches = searchTerms.some(term => 
          titleLower.includes(term) || descriptionLower.includes(term)
        );
        
        if (matches) {
          results.push({
            id: doc.id,
            ...listing
          });
        }
      });
      
      console.log(`searchListings returning ${results.length} results`);
      
      return {
        success: true,
        data: {
          listings: results.slice(0, limit),
          total: results.length
        }
      };
    } catch (error) {
      console.error('searchListings error:', error);
      return handleError(error);
    }
  });
