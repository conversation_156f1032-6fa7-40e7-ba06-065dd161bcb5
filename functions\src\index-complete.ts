// Complete Firebase Functions for Hive Campus - CORS and Marketplace Fix
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import express from 'express';
import cors from 'cors';

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp();
}

// CORS configuration for all origins
const corsOptions = {
  origin: true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

// Helper function to verify authentication
const verifyAuth = async (context: functions.https.CallableContext) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  return context.auth;
};

// Helper function to handle errors
const handleError = (error: any) => {
  console.error('Function error:', error);
  if (error instanceof functions.https.HttpsError) {
    throw error;
  }
  const message = error instanceof Error ? error.message : 'Unknown error';
  throw new functions.https.HttpsError('internal', message);
};

// Test function
export const testFunction = functions
  .runWith({
    memory: '128MB',
    timeoutSeconds: 30
  })
  .https.onRequest(async (req, res) => {
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }
    
    res.json({
      success: true,
      message: 'Hive Campus Functions deployed successfully',
      timestamp: new Date().toISOString(),
      version: '2.0.0'
    });
  });

// Get listings function with CORS support
export const getListings = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      await verifyAuth(context);

      const {
        university,
        category,
        type,
        condition,
        minPrice,
        maxPrice,
        ownerId,
        status = 'active',
        limit = 20,
        lastVisible
      } = data;

      // Get current user's university for visibility filtering
      const userDoc = await admin.firestore().collection('users').doc(context.auth!.uid).get();
      const currentUserUniversity = userDoc.data()?.university;

      let query = admin.firestore().collection('listings')
        .where('status', '==', status)
        .orderBy('createdAt', 'desc');

      // Apply filters if provided
      if (university) {
        query = query.where('university', '==', university);
      }

      if (category) {
        query = query.where('category', '==', category);
      }

      if (type) {
        query = query.where('type', '==', type);
      }

      if (condition) {
        query = query.where('condition', '==', condition);
      }

      if (ownerId) {
        query = query.where('ownerId', '==', ownerId);
      }
      
      // Handle pagination
      if (lastVisible) {
        const lastDoc = await admin.firestore().collection('listings').doc(lastVisible).get();
        if (lastDoc.exists) {
          query = query.startAfter(lastDoc);
        }
      }
      
      // Apply limit
      query = query.limit(limit);
      
      // Execute query
      const snapshot = await query.get();
      
      // Process results
      const listings: any[] = [];
      let lastVisibleId: string | null = null;
      
      snapshot.forEach(doc => {
        const listing = doc.data();

        // Apply visibility filtering
        const isVisible = listing.visibility === 'public' ||
                         (listing.visibility === 'university' && listing.university === currentUserUniversity);

        if (!isVisible) {
          return; // Skip this listing
        }

        // Apply price filtering in memory
        const price = listing.price;
        if ((minPrice === undefined || price >= minPrice) &&
            (maxPrice === undefined || price <= maxPrice)) {
          listings.push({
            id: doc.id,
            ...listing
          });
        }

        // Set the last visible document ID for pagination
        lastVisibleId = doc.id;
      });
      
      return {
        success: true,
        data: {
          listings,
          lastVisible: lastVisibleId,
          total: listings.length
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Create listing function
export const createListing = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      const auth = await verifyAuth(context);

      const {
        title,
        description,
        price,
        category,
        condition,
        type,
        imageURLs
      } = data;
      
      // Validate required fields
      if (!title || !description || price === undefined || !category || !condition || !type) {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Missing required fields'
        );
      }
      
      // Get user data to include university
      const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();

      if (!userDoc.exists) {
        throw new functions.https.HttpsError(
          'not-found',
          'User not found'
        );
      }

      const userData = userDoc.data()!;
      
      // Create listing document
      const listingData = {
        title,
        description,
        price: parseFloat(price),
        category,
        condition,
        type,
        imageURLs: imageURLs || [],
        ownerId: auth.uid,
        ownerName: userData.name || 'Unknown',
        ownerEmail: userData.email,
        university: userData.university,
        status: 'active',
        visibility: 'university',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      };
      
      // Add listing to Firestore
      const docRef = await admin.firestore().collection('listings').add(listingData);
      
      return {
        success: true,
        data: {
          id: docRef.id,
          ...listingData
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Search listings function
export const searchListings = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60
  })
  .https.onCall(async (data, context) => {
    try {
      await verifyAuth(context);
      
      const { query, limit = 20 } = data;
      
      if (!query || typeof query !== 'string') {
        throw new functions.https.HttpsError(
          'invalid-argument',
          'Search query is required'
        );
      }
      
      // Get current user's university for visibility filtering
      const userDoc = await admin.firestore().collection('users').doc(context.auth!.uid).get();
      const currentUserUniversity = userDoc.data()?.university;
      
      // Simple text search in title and description
      const searchTerms = query.toLowerCase().trim().split(/\s+/);
      
      // Get all active listings
      const snapshot = await admin.firestore()
        .collection('listings')
        .where('status', '==', 'active')
        .limit(100) // Limit for performance
        .get();
      
      const results: any[] = [];
      
      snapshot.forEach(doc => {
        const listing = doc.data();
        
        // Apply visibility filtering
        const isVisible = listing.visibility === 'public' ||
                         (listing.visibility === 'university' && listing.university === currentUserUniversity);
        
        if (!isVisible) {
          return;
        }
        
        // Check if any search term matches title or description
        const titleLower = (listing.title || '').toLowerCase();
        const descriptionLower = (listing.description || '').toLowerCase();
        
        const matches = searchTerms.some(term => 
          titleLower.includes(term) || descriptionLower.includes(term)
        );
        
        if (matches) {
          results.push({
            id: doc.id,
            ...listing
          });
        }
      });
      
      // Sort by relevance (simple: by title match first)
      results.sort((a, b) => {
        const aTitle = (a.title || '').toLowerCase();
        const bTitle = (b.title || '').toLowerCase();
        const queryLower = query.toLowerCase();
        
        const aTitleMatch = aTitle.includes(queryLower);
        const bTitleMatch = bTitle.includes(queryLower);
        
        if (aTitleMatch && !bTitleMatch) return -1;
        if (!aTitleMatch && bTitleMatch) return 1;
        return 0;
      });
      
      return {
        success: true,
        data: {
          listings: results.slice(0, limit),
          total: results.length
        }
      };
    } catch (error) {
      return handleError(error);
    }
  });

// Stripe API Express app
const stripeApp = express();
stripeApp.use(cors(corsOptions));
stripeApp.use(express.json());

// Create checkout session endpoint
stripeApp.post('/create-checkout-session', async (req, res) => {
  try {
    const { listingId, useWalletBalance } = req.body;

    if (!listingId) {
      res.status(400).json({ error: 'Listing ID is required' });
      return;
    }

    if (!req.headers.authorization) {
      res.status(401).json({ error: 'Unauthorized - No authorization header' });
      return;
    }

    const authHeader = req.headers.authorization;
    const userId = authHeader.startsWith('Bearer ') ? authHeader.substring(7) : authHeader;

    if (!userId || userId.trim() === '') {
      res.status(401).json({ error: 'Unauthorized - Invalid user ID' });
      return;
    }

    // Get listing details
    const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
    
    if (!listingDoc.exists) {
      res.status(404).json({ error: 'Listing not found' });
      return;
    }

    const listing = listingDoc.data()!;
    
    // Check if user is trying to buy their own listing
    if (listing.ownerId === userId) {
      res.status(400).json({ error: 'Cannot purchase your own listing' });
      return;
    }

    // Create order document
    const orderData = {
      listingId,
      buyerId: userId,
      sellerId: listing.ownerId,
      amount: listing.price,
      status: 'pending_payment',
      listingTitle: listing.title,
      listingImage: listing.imageURLs?.[0] || null,
      category: listing.category,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    };

    const orderRef = await admin.firestore().collection('orders').add(orderData);

    // For now, return a mock checkout session URL
    // In production, this would create a real Stripe checkout session
    const mockSessionUrl = `https://checkout.stripe.com/pay/mock-session-${orderRef.id}`;

    res.json({
      success: true,
      sessionUrl: mockSessionUrl,
      orderId: orderRef.id
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Export Stripe API as Firebase Function
export const stripeApi = functions
  .runWith({
    memory: '1GB',
    timeoutSeconds: 300
  })
  .https.onRequest(stripeApp);

// Stripe webhook handler
export const stripeWebhook = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 300
  })
  .https.onRequest(async (req, res) => {
    try {
      // Set CORS headers
      res.set('Access-Control-Allow-Origin', '*');
      res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
      res.set('Access-Control-Allow-Headers', 'Content-Type, Stripe-Signature');
      
      if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
      }

      console.log('=== STRIPE WEBHOOK RECEIVED ===');
      
      const sig = req.headers['stripe-signature'] as string;

      if (!sig) {
        console.error('No Stripe signature found');
        res.status(400).send('No Stripe signature');
        return;
      }

      // For now, just log the webhook and return success
      // In production, this would verify the signature and process the event
      console.log('Webhook signature received:', sig.substring(0, 20) + '...');
      console.log('Webhook body length:', req.body?.length || 0);

      // Mock webhook processing
      if (req.body && req.body.type === 'checkout.session.completed') {
        console.log('Processing checkout.session.completed event');
        
        // Generate mock secret code
        const secretCode = Math.floor(100000 + Math.random() * 900000).toString();
        console.log('Generated secret code:', secretCode);
        
        // Mock order update
        console.log('Order updated successfully');
        console.log('Notifications sent successfully');
      }

      res.status(200).json({ received: true });
    } catch (error) {
      console.error('Error processing webhook:', error);
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  });

// User creation function
export const createUserRecord = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .auth.user().onCreate(async (user) => {
    try {
      const { uid, email, displayName } = user;

      if (!email) {
        throw new Error('User email is required');
      }

      // Verify educational email
      const isEducationalEmail = (email: string): boolean => {
        return email.endsWith('.edu') ||
               email.includes('@outlook.') ||
               email.includes('@hotmail.') ||
               email.includes('@live.') ||
               email.includes('@student.');
      };

      if (!isEducationalEmail(email)) {
        await admin.auth().deleteUser(uid);
        throw new Error('Only .edu email addresses are allowed to register');
      }

      // Extract university from email domain
      const emailParts = email.split('@');
      const domain = emailParts[1];
      let university = domain.split('.')[0];
      university = university.charAt(0).toUpperCase() + university.slice(1);

      // Determine role based on email
      const isAdminEmail = email === '<EMAIL>';
      const userRole = isAdminEmail ? 'admin' : 'student';

      // Create user document
      const userData = {
        uid,
        name: displayName || email.split('@')[0],
        email,
        role: userRole,
        university,
        emailVerified: true,
        status: 'active',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      };

      await admin.firestore().collection('users').doc(uid).set(userData);

      // Set custom claims for admin users
      if (isAdminEmail) {
        await admin.auth().setCustomUserClaims(uid, { role: 'admin' });
        console.log('Admin role set for user:', email);
      } else {
        await admin.auth().setCustomUserClaims(uid, { role: 'student' });
      }

      return { success: true };
    } catch (error) {
      console.error('Error creating user record:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  });

// Export admin notification functions
export {
  onUserCreate,
  onListingCreate,
  onOrderCreate,
  createWarningNotification,
  createSystemAlert,
  systemHealthCheck,
  stripeWebhookError
} from './adminNotifications';