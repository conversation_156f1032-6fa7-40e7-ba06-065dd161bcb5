# Quick Webhook Test Script
Write-Host "🧪 TESTING STRIPE WEBHOOK ENDPOINT" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Test 1: Basic endpoint availability
Write-Host "`n1️⃣ Testing webhook endpoint availability..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook" -Method GET -ErrorAction SilentlyContinue
} catch {
    if ($_.Exception.Message -like "*No Stripe signature*") {
        Write-Host "✅ PASS: Webhook endpoint is responding correctly" -ForegroundColor Green
        Write-Host "   Response: 'No Stripe signature' (expected behavior)" -ForegroundColor Gray
    } else {
        Write-Host "❌ FAIL: Unexpected response: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 2: Check Firebase Functions logs
Write-Host "`n2️⃣ Checking recent webhook logs..." -ForegroundColor Yellow
Write-Host "Running: firebase functions:log --only stripeWebhook" -ForegroundColor Gray
firebase functions:log --only stripeWebhook | Select-String -Pattern "webhook secret|Using webhook secret" | Select-Object -Last 3

# Test 3: Verify configuration
Write-Host "`n3️⃣ Verifying Firebase configuration..." -ForegroundColor Yellow
Write-Host "Checking stripe.webhook_secret configuration..." -ForegroundColor Gray
$config = firebase functions:config:get | ConvertFrom-Json
if ($config.stripe.webhook_secret -eq "whsec_Ggd0wEt8z8NzRV5kyEyvXH2iB1xrWSZq") {
    Write-Host "✅ PASS: New webhook secret is configured correctly" -ForegroundColor Green
} else {
    Write-Host "❌ FAIL: Webhook secret mismatch in configuration" -ForegroundColor Red
}

Write-Host "`n🎯 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Update Stripe Dashboard with new secret" -ForegroundColor White
Write-Host "2. Test with real payment flow" -ForegroundColor White
Write-Host "3. Monitor webhook delivery in Stripe Dashboard" -ForegroundColor White