import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Menu, Sun, Moon, Bell, Search, Monitor, ChevronDown } from 'lucide-react';
import { useAdminTheme } from './AdminThemeContext';
import AdminNotificationService from '../../services/AdminNotificationService';

interface AdminHeaderProps {
  onMenuClick: () => void;
  currentPath: string;
}

const AdminHeader: React.FC<AdminHeaderProps> = ({ onMenuClick, currentPath }) => {
  const { isDark, toggleTheme: _toggleTheme, setTheme } = useAdminTheme();
  const [themeDropdownOpen, setThemeDropdownOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const themeDropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const notificationService = AdminNotificationService.getInstance();

  // Subscribe to unread notification count
  useEffect(() => {
    const unsubscribe = notificationService.subscribeToUnreadCount(
      (count) => setUnreadCount(count),
      (error) => console.error('Error subscribing to unread count:', error)
    );

    return () => unsubscribe();
  }, [notificationService]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (themeDropdownRef.current && !themeDropdownRef.current.contains(event.target as Node)) {
        setThemeDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Get page title from current path
  const getPageTitle = (path: string) => {
    const pathMap: Record<string, string> = {
      '/admin': 'Overview',
      '/admin/overview': 'Overview',
      '/admin/users': 'User Management',
      '/admin/universities': 'Universities',
      '/admin/listings': 'Listings Management',
      '/admin/transactions': 'Transactions',
      '/admin/chat': 'Chat & Messaging',
      '/admin/shipping': 'Shipping Management',
      '/admin/analytics': 'Analytics',
      '/admin/reports': 'Reports & Moderation',
      '/admin/reeflex': 'ReeFlex Insights',
      '/admin/reeflex-chat': 'ReeFlex AI Chat',
      '/admin/settings': 'System Settings',
      '/admin/notifications': 'Notifications',
    };
    return pathMap[path] || 'Admin Panel';
  };

  const getPageDescription = (path: string) => {
    const descriptionMap: Record<string, string> = {
      '/admin': 'Platform overview and key metrics',
      '/admin/overview': 'Platform overview and key metrics',
      '/admin/notifications': 'Real-time admin notifications and alerts',
      '/admin/users': 'Manage users, profiles, and accounts',
      '/admin/universities': 'Manage universities and domains',
      '/admin/listings': 'Review and moderate listings',
      '/admin/transactions': 'Monitor payments and transactions',
      '/admin/chat': 'Monitor conversations and messaging',
      '/admin/shipping': 'Manage shipping labels and tracking',
      '/admin/analytics': 'Platform analytics and insights',
      '/admin/reports': 'Content moderation and reports',
      '/admin/reeflex': 'AI-powered insights and feedback',
      '/admin/reeflex-chat': 'Chat with your AI engineering team',
      '/admin/settings': 'System configuration and settings',
    };
    return descriptionMap[path] || 'Hive Campus Administration';
  };

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
        {/* Left side - Mobile menu button and page title */}
        <div className="flex items-center">
          <button
            type="button"
            className="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            onClick={onMenuClick}
          >
            <span className="sr-only">Open sidebar</span>
            <Menu className="h-6 w-6" />
          </button>
          
          <div className="ml-4 lg:ml-0">
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              {getPageTitle(currentPath)}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {getPageDescription(currentPath)}
            </p>
          </div>
        </div>

        {/* Right side - Search, notifications, theme toggle */}
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="hidden md:block">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="admin-header-search"
                name="search"
                type="text"
                placeholder="Search..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>

          {/* Notifications */}
          <button
            type="button"
            onClick={() => navigate('/admin/notifications')}
            className="relative p-1 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <span className="sr-only">View notifications</span>
            <Bell className="h-6 w-6" />
            {/* Notification badge */}
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 flex items-center justify-center h-5 w-5 text-xs font-medium text-white bg-red-500 rounded-full ring-2 ring-white dark:ring-gray-800">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </button>

          {/* Theme selector */}
          <div className="relative" ref={themeDropdownRef}>
            <button
              type="button"
              onClick={() => setThemeDropdownOpen(!themeDropdownOpen)}
              className="flex items-center p-1 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              title="Change theme"
            >
              <span className="sr-only">Change theme</span>
              {isDark ? (
                <Moon className="h-5 w-5" />
              ) : (
                <Sun className="h-5 w-5" />
              )}
              <ChevronDown className="h-3 w-3 ml-1" />
            </button>

            {/* Theme dropdown */}
            {themeDropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                <div className="py-1">
                  <button
                    onClick={() => {
                      setTheme('light');
                      setThemeDropdownOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <Sun className="h-4 w-4 mr-3" />
                    Light
                  </button>
                  <button
                    onClick={() => {
                      setTheme('dark');
                      setThemeDropdownOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <Moon className="h-4 w-4 mr-3" />
                    Dark
                  </button>
                  <button
                    onClick={() => {
                      setTheme('system');
                      setThemeDropdownOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <Monitor className="h-4 w-4 mr-3" />
                    System
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default AdminHeader;
