import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Bell,
  User,
  Package,
  CreditCard,
  AlertTriangle,
  XCircle,
  Shield,
  Activity,
  Settings,
  Zap,
  CheckCircle2,
  Trash2,
  Eye,
  ExternalLink,
  Clock,
  Filter
} from 'lucide-react';
import { AdminNotification, AdminNotificationType } from '../../../firebase/types';
import AdminNotificationService from '../../../services/AdminNotificationService';
import { formatDistanceToNow } from 'date-fns';

const AdminNotifications: React.FC = () => {
  const [notifications, setNotifications] = useState<AdminNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'unread' | AdminNotificationType>('all');
  const [groupBy, setGroupBy] = useState<'type' | 'date'>('type');
  const navigate = useNavigate();
  const notificationService = AdminNotificationService.getInstance();

  useEffect(() => {
    const unsubscribe = notificationService.subscribeToNotifications(
      (newNotifications) => {
        setNotifications(newNotifications);
        setLoading(false);
      },
      (error) => {
        console.error('Error loading notifications:', error);
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, []);

  const getNotificationIcon = (type: AdminNotificationType) => {
    const iconMap: Record<AdminNotificationType, React.ReactNode> = {
      new_user: <User className="h-5 w-5 text-blue-500" />,
      new_listing: <Package className="h-5 w-5 text-green-500" />,
      purchase: <CreditCard className="h-5 w-5 text-purple-500" />,
      warning: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
      error: <XCircle className="h-5 w-5 text-red-500" />,
      stripe_webhook: <CreditCard className="h-5 w-5 text-orange-500" />,
      payment_failed: <XCircle className="h-5 w-5 text-red-500" />,
      suspicious_activity: <Shield className="h-5 w-5 text-red-600" />,
      listing_flagged: <AlertTriangle className="h-5 w-5 text-yellow-600" />,
      user_blocked: <User className="h-5 w-5 text-red-500" />,
      system_alert: <Bell className="h-5 w-5 text-blue-600" />,
      maintenance: <Settings className="h-5 w-5 text-gray-500" />,
      security: <Shield className="h-5 w-5 text-red-700" />,
      performance: <Zap className="h-5 w-5 text-yellow-500" />
    };
    return iconMap[type] || <Bell className="h-5 w-5 text-gray-500" />;
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();
    } catch (error) {
      console.error('Error marking all as read:', error);
    }
  };

  const handleClearAll = async () => {
    if (window.confirm('Are you sure you want to clear all notifications? This action cannot be undone.')) {
      try {
        await notificationService.clearAll();
      } catch (error) {
        console.error('Error clearing notifications:', error);
      }
    }
  };

  const handleNotificationClick = (notification: AdminNotification) => {
    // Mark as read if not already read
    if (!notification.read && notification.id) {
      handleMarkAsRead(notification.id);
    }

    // Navigate to related page based on notification type and metadata
    const { type, metadata } = notification;
    
    if (metadata?.actionUrl) {
      navigate(metadata.actionUrl);
      return;
    }

    switch (type) {
      case 'new_user':
        if (metadata?.userId) {
          navigate(`/admin/users?search=${metadata.userId}`);
        } else {
          navigate('/admin/users');
        }
        break;
      case 'new_listing':
        if (metadata?.listingId) {
          navigate(`/admin/listings?search=${metadata.listingId}`);
        } else {
          navigate('/admin/listings');
        }
        break;
      case 'purchase':
        if (metadata?.orderId) {
          navigate(`/admin/transactions?search=${metadata.orderId}`);
        } else {
          navigate('/admin/transactions');
        }
        break;
      case 'listing_flagged':
        navigate('/admin/reports');
        break;
      case 'user_blocked':
        navigate('/admin/users');
        break;
      default:
        navigate('/admin/dashboard');
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true;
    if (filter === 'unread') return !notification.read;
    return notification.type === filter;
  });

  const groupedNotifications = groupBy === 'type' 
    ? filteredNotifications.reduce((groups, notification) => {
        const type = notification.type;
        if (!groups[type]) groups[type] = [];
        groups[type].push(notification);
        return groups;
      }, {} as Record<string, AdminNotification[]>)
    : { 'All Notifications': filteredNotifications };

  const unreadCount = notifications.filter(n => !n.read).length;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Admin Notifications
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All notifications read'}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          {unreadCount > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <CheckCircle2 className="h-4 w-4 mr-2" />
              Mark All Read
            </button>
          )}
          <button
            onClick={handleClearAll}
            className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear All
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Filter:</span>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All</option>
            <option value="unread">Unread</option>
            <option value="new_user">New Users</option>
            <option value="new_listing">New Listings</option>
            <option value="purchase">Purchases</option>
            <option value="warning">Warnings</option>
            <option value="error">Errors</option>
            <option value="suspicious_activity">Suspicious Activity</option>
          </select>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Group by:</span>
          <select
            value={groupBy}
            onChange={(e) => setGroupBy(e.target.value as any)}
            className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="type">Type</option>
            <option value="date">Date</option>
          </select>
        </div>
      </div>

      {/* Notifications */}
      {Object.keys(groupedNotifications).length === 0 ? (
        <div className="text-center py-12">
          <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No notifications
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            You're all caught up! No notifications to display.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedNotifications).map(([groupName, groupNotifications]) => (
            <div key={groupName} className="space-y-3">
              {groupBy === 'type' && (
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white capitalize">
                  {groupName.replace(/_/g, ' ')} ({groupNotifications.length})
                </h2>
              )}
              
              <div className="space-y-2">
                {groupNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                      notification.read
                        ? 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                        : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className={`text-sm font-medium ${
                            notification.read ? 'text-gray-900 dark:text-white' : 'text-blue-900 dark:text-blue-100'
                          }`}>
                            {notification.title}
                          </h3>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDistanceToNow(notification.createdAt.toDate(), { addSuffix: true })}
                            </span>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {notification.message}
                        </p>
                        
                        {notification.metadata && (
                          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                            {notification.metadata.userName && (
                              <span>User: {notification.metadata.userName}</span>
                            )}
                            {notification.metadata.listingTitle && (
                              <span>Listing: {notification.metadata.listingTitle}</span>
                            )}
                            {notification.metadata.orderAmount && (
                              <span>Amount: ${notification.metadata.orderAmount}</span>
                            )}
                            {notification.metadata.severity && (
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                notification.metadata.severity === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                                notification.metadata.severity === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                                notification.metadata.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                              }`}>
                                {notification.metadata.severity}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-shrink-0">
                        <ExternalLink className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AdminNotifications;
