import React from 'react';
import { ReeFlexMultiChat } from '../reeflex/ReeFlexMultiChat';

const AdminReeFlexChat: React.FC = () => {
  return (
    <div className="h-full flex flex-col">
      {/* Page Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              ReeFlex AI Engineering Chat
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Chat with your multi-agent AI engineering team about Hive Campus
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900 dark:to-blue-900 px-4 py-2 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  3 AI Models Active
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Interface */}
      <div className="flex-1 overflow-hidden">
        <ReeFlexMultiChat />
      </div>
    </div>
  );
};

export default AdminReeFlexChat;
