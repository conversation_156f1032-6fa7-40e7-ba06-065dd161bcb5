import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, Clock, Z<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Co<PERSON>, Check, TestTube } from 'lucide-react';
import { MultiModelService } from '../../../services/reeflex/MultiModelService';
import { MultiModelRequest, MultiModelResponse, AIModel, ModelResponse } from '../../../services/reeflex/types';
import { useAuth } from '../../../hooks/useAuth';
import { ApiTester } from '../../../services/reeflex/ApiTester';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  responses?: ModelResponse[];
  verdict?: string;
  isLoading?: boolean;
}

interface ModelToggle {
  model: AIModel;
  enabled: boolean;
  name: string;
  icon: string;
  color: string;
}

export const ReeFlexMultiChat: React.FC = () => {
  const { userProfile } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [modelToggles, setModelToggles] = useState<ModelToggle[]>([
    { model: 'openai', enabled: true, name: 'OpenAI GPT-4', icon: '🤖', color: 'bg-green-500' },
    { model: 'gemini', enabled: true, name: 'Google Gemini', icon: '🔮', color: 'bg-blue-500' },
    { model: 'openrouter', enabled: true, name: 'OpenRouter (Claude/LLaMA)', icon: '🌐', color: 'bg-purple-500' }
  ]);
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});
  const [isTestingApis, setIsTestingApis] = useState(false);
  const [apiTestResults, setApiTestResults] = useState<any>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const multiModelService = useRef(new MultiModelService());

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const toggleModel = (model: AIModel) => {
    setModelToggles(prev => 
      prev.map(toggle => 
        toggle.model === model 
          ? { ...toggle, enabled: !toggle.enabled }
          : toggle
      )
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;

    const enabledModels = modelToggles
      .filter(toggle => toggle.enabled)
      .map(toggle => toggle.model);

    if (enabledModels.length === 0) {
      alert('Please enable at least one AI model');
      return;
    }

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    const loadingMessage: Message = {
      id: `assistant-${Date.now()}`,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      isLoading: true
    };

    setMessages(prev => [...prev, userMessage, loadingMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Gather system context
      const context = await multiModelService.current.gatherSystemContext();
      
      const request: MultiModelRequest = {
        prompt: inputValue,
        models: enabledModels,
        temperature: 0.7,
        maxTokens: 2000,
        context
      };

      const response = await multiModelService.current.processMultiModelRequest(request);

      // Log the conversation to Firebase
      if (userProfile?.uid) {
        await multiModelService.current.logConversation(
          userProfile.uid,
          inputValue,
          response
        );
      }

      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        type: 'assistant',
        content: response.verdict,
        timestamp: new Date(),
        responses: response.responses,
        verdict: response.verdict
      };

      setMessages(prev => prev.slice(0, -1).concat(assistantMessage));
    } catch (error) {
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        type: 'assistant',
        content: `❌ Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        timestamp: new Date()
      };

      setMessages(prev => prev.slice(0, -1).concat(errorMessage));
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates(prev => ({ ...prev, [id]: true }));
      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [id]: false }));
      }, 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const testApiConnections = async () => {
    setIsTestingApis(true);
    try {
      const results = await ApiTester.testAllApis();
      setApiTestResults(results);

      // Add test results as a message
      const testMessage: Message = {
        id: `test-${Date.now()}`,
        type: 'assistant',
        content: `🔧 **API Connection Test Results**\n\n**OpenAI**: ${results.openai.success ? '✅ Connected' : `❌ Failed - ${results.openai.error}`}\n\n**Gemini**: ${results.gemini.success ? '✅ Connected' : `❌ Failed - ${results.gemini.error}`}\n\n**OpenRouter**: ${results.openrouter.success ? '✅ Connected' : `❌ Failed - ${results.openrouter.error}`}\n\n${results.openai.success || results.gemini.success || results.openrouter.success ? '**Status**: At least one model is working! You can proceed with ReeFlex.' : '**Status**: All models failed. Please check your API keys and internet connection.'}`,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, testMessage]);
    } catch (error) {
      console.error('Error testing APIs:', error);
    } finally {
      setIsTestingApis(false);
    }
  };

  const formatLatency = (latency: number) => {
    return latency < 1000 ? `${latency}ms` : `${(latency / 1000).toFixed(1)}s`;
  };

  return (
    <div className="flex flex-col h-full bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">ReeFlex Multi-Agent Chat</h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">Your AI engineering team</p>
            </div>
          </div>
          
          {/* Model Toggles and Test Button */}
          <div className="flex items-center space-x-2">
            <button
              onClick={testApiConnections}
              disabled={isTestingApis}
              className="px-3 py-2 rounded-lg text-sm font-medium bg-yellow-500 hover:bg-yellow-600 text-white transition-all duration-200 flex items-center space-x-1"
              title="Test API Connections"
            >
              {isTestingApis ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <TestTube className="w-4 h-4" />
              )}
              <span>Test APIs</span>
            </button>

            {modelToggles.map((toggle) => (
              <button
                key={toggle.model}
                onClick={() => toggleModel(toggle.model)}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  toggle.enabled
                    ? `${toggle.color} text-white shadow-lg`
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                }`}
                title={toggle.name}
              >
                <span className="mr-1">{toggle.icon}</span>
                {toggle.name.split(' ')[0]}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="text-center py-12">
            <Brain className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Welcome to ReeFlex
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
              Ask me about bugs, performance issues, growth strategies, or anything related to Hive Campus. 
              I'll consult with multiple AI models to give you the best insights.
            </p>
          </div>
        )}

        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-4xl ${message.type === 'user' ? 'ml-12' : 'mr-12'}`}>
              {/* Message Header */}
              <div className="flex items-center space-x-2 mb-2">
                {message.type === 'user' ? (
                  <User className="w-5 h-5 text-blue-500" />
                ) : (
                  <Bot className="w-5 h-5 text-purple-500" />
                )}
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {message.type === 'user' ? 'You' : 'ReeFlex'}
                </span>
                <span className="text-xs text-gray-500">
                  {message.timestamp.toLocaleTimeString()}
                </span>
              </div>

              {/* Message Content */}
              <div className={`rounded-lg p-4 ${
                message.type === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
              }`}>
                {message.isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500"></div>
                    <span className="text-gray-600 dark:text-gray-400">ReeFlex is thinking...</span>
                  </div>
                ) : (
                  <div className="prose dark:prose-invert max-w-none">
                    <ReactMarkdown
                      components={{
                        code({ node, inline, className, children, ...props }) {
                          const match = /language-(\w+)/.exec(className || '');
                          return !inline && match ? (
                            <SyntaxHighlighter
                              style={oneDark}
                              language={match[1]}
                              PreTag="div"
                              {...props}
                            >
                              {String(children).replace(/\n$/, '')}
                            </SyntaxHighlighter>
                          ) : (
                            <code className={className} {...props}>
                              {children}
                            </code>
                          );
                        }
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                  </div>
                )}

                {/* Copy Button */}
                {!message.isLoading && message.content && (
                  <button
                    onClick={() => copyToClipboard(message.content, message.id)}
                    className="mt-2 flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    {copiedStates[message.id] ? (
                      <Check className="w-3 h-3" />
                    ) : (
                      <Copy className="w-3 h-3" />
                    )}
                    <span>{copiedStates[message.id] ? 'Copied!' : 'Copy'}</span>
                  </button>
                )}
              </div>

              {/* Model Responses */}
              {message.responses && message.responses.length > 0 && (
                <div className="mt-4 space-y-3">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Individual Model Responses:
                  </h4>
                  {message.responses.map((response, index) => {
                    const toggle = modelToggles.find(t => response.model.includes(t.model));
                    return (
                      <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span>{toggle?.icon || '🤖'}</span>
                            <span className="text-sm font-medium">{response.model}</span>
                            {response.error && (
                              <span className="text-xs text-red-500 bg-red-100 dark:bg-red-900 px-2 py-1 rounded">
                                Error
                              </span>
                            )}
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-gray-500">
                            <Clock className="w-3 h-3" />
                            <span>{formatLatency(response.latency)}</span>
                            {response.tokens && (
                              <span>{response.tokens.total} tokens</span>
                            )}
                          </div>
                        </div>
                        {response.error ? (
                          <p className="text-sm text-red-600 dark:text-red-400">{response.error}</p>
                        ) : (
                          <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-3">
                            {response.content.substring(0, 200)}...
                          </p>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Form */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
        <form onSubmit={handleSubmit} className="flex space-x-3">
          <div className="flex-1">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Ask ReeFlex about bugs, performance, growth strategies, or anything about Hive Campus..."
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
              rows={3}
              disabled={isLoading}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmit(e);
                }
              }}
            />
          </div>
          <button
            type="submit"
            disabled={isLoading || !inputValue.trim()}
            className="px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-600 text-white rounded-lg hover:from-purple-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            ) : (
              <Send className="w-5 h-5" />
            )}
            <span>Send</span>
          </button>
        </form>
        
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          Press Enter to send, Shift+Enter for new line. 
          {modelToggles.filter(t => t.enabled).length} model(s) enabled.
        </div>
      </div>
    </div>
  );
};
