import {
  collection,
  query,
  orderBy,
  onSnapshot,
  doc,
  updateDoc,
  writeBatch,
  where,
  addDoc,
  Timestamp,
  Unsubscribe,
  getDocs
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import { firestore, functions } from '../firebase/config';
import { AdminNotification, AdminNotificationType } from '../firebase/types';

export class AdminNotificationService {
  private static instance: AdminNotificationService;
  private unsubscribe: Unsubscribe | null = null;

  static getInstance(): AdminNotificationService {
    if (!AdminNotificationService.instance) {
      AdminNotificationService.instance = new AdminNotificationService();
    }
    return AdminNotificationService.instance;
  }

  /**
   * Listen to real-time admin notifications
   */
  subscribeToNotifications(
    callback: (notifications: AdminNotification[]) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    try {
      const notificationsRef = collection(firestore, 'adminNotifications');
      const q = query(notificationsRef, orderBy('createdAt', 'desc'));

      this.unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          const notifications: AdminNotification[] = [];
          snapshot.forEach((doc) => {
            notifications.push({
              id: doc.id,
              ...doc.data()
            } as AdminNotification);
          });
          callback(notifications);
        },
        (error) => {
          console.error('Error listening to admin notifications:', error);
          if (onError) onError(error);
        }
      );

      return this.unsubscribe;
    } catch (error) {
      console.error('Error setting up notifications listener:', error);
      if (onError) onError(error as Error);
      return () => {};
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount(): Promise<number> {
    try {
      const notificationsRef = collection(firestore, 'adminNotifications');
      const q = query(notificationsRef, where('read', '==', false));
      const snapshot = await getDocs(q);
      return snapshot.size;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  /**
   * Subscribe to unread count changes
   */
  subscribeToUnreadCount(
    callback: (count: number) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    try {
      const notificationsRef = collection(firestore, 'adminNotifications');
      const q = query(notificationsRef, where('read', '==', false));

      return onSnapshot(
        q,
        (snapshot) => {
          callback(snapshot.size);
        },
        (error) => {
          console.error('Error listening to unread count:', error);
          if (onError) onError(error);
        }
      );
    } catch (error) {
      console.error('Error setting up unread count listener:', error);
      if (onError) onError(error as Error);
      return () => {};
    }
  }

  /**
   * Mark a notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const notificationRef = doc(firestore, 'adminNotifications', notificationId);
      await updateDoc(notificationRef, {
        read: true,
        readAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<void> {
    try {
      const notificationsRef = collection(firestore, 'adminNotifications');
      const q = query(notificationsRef, where('read', '==', false));
      const snapshot = await getDocs(q);

      if (snapshot.empty) return;

      const batch = writeBatch(firestore);
      const now = Timestamp.now();

      snapshot.forEach((doc) => {
        batch.update(doc.ref, {
          read: true,
          readAt: now
        });
      });

      await batch.commit();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Clear all notifications (delete them)
   */
  async clearAll(): Promise<void> {
    try {
      const notificationsRef = collection(firestore, 'adminNotifications');
      const snapshot = await getDocs(notificationsRef);

      if (snapshot.empty) return;

      const batch = writeBatch(firestore);

      snapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error clearing all notifications:', error);
      throw error;
    }
  }

  /**
   * Create a new admin notification
   */
  async createNotification(
    type: AdminNotificationType,
    message: string,
    title?: string,
    metadata?: AdminNotification['metadata']
  ): Promise<void> {
    try {
      const notificationsRef = collection(firestore, 'adminNotifications');
      await addDoc(notificationsRef, {
        type,
        message,
        title: title || this.getDefaultTitle(type),
        createdAt: Timestamp.now(),
        read: false,
        metadata: metadata || {}
      });
    } catch (error) {
      console.error('Error creating admin notification:', error);
      throw error;
    }
  }

  /**
   * Get default title for notification type
   */
  private getDefaultTitle(type: AdminNotificationType): string {
    const titles: Record<AdminNotificationType, string> = {
      new_user: '👤 New User Registration',
      new_listing: '📦 New Listing Created',
      purchase: '💰 Purchase Completed',
      warning: '⚠️ System Warning',
      error: '❌ System Error',
      stripe_webhook: '💳 Stripe Webhook Issue',
      payment_failed: '💸 Payment Failed',
      suspicious_activity: '🚨 Suspicious Activity',
      listing_flagged: '🚩 Listing Flagged',
      user_blocked: '🚫 User Action Required',
      system_alert: '🔔 System Alert',
      maintenance: '🔧 Maintenance Notice',
      security: '🔒 Security Alert',
      performance: '⚡ Performance Issue'
    };
    return titles[type] || '📢 Admin Notification';
  }

  /**
   * Create a warning notification via Firebase Function
   */
  async createWarning(
    message: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const createWarningNotification = httpsCallable(functions, 'createWarningNotification');
      await createWarningNotification({
        message,
        severity,
        metadata
      });
    } catch (error) {
      console.error('Error creating warning notification:', error);
      throw error;
    }
  }

  /**
   * Create a system alert via Firebase Function
   */
  async createSystemAlert(
    type: AdminNotificationType,
    message: string,
    title?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const createSystemAlert = httpsCallable(functions, 'createSystemAlert');
      await createSystemAlert({
        type,
        message,
        title,
        metadata
      });
    } catch (error) {
      console.error('Error creating system alert:', error);
      throw error;
    }
  }

  /**
   * Report suspicious activity
   */
  async reportSuspiciousActivity(
    description: string,
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await this.createSystemAlert(
        'suspicious_activity',
        description,
        '🚨 Suspicious Activity Detected',
        {
          ...metadata,
          userId,
          severity: 'high',
          timestamp: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('Error reporting suspicious activity:', error);
      throw error;
    }
  }

  /**
   * Report system error
   */
  async reportSystemError(
    error: string,
    context?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await this.createSystemAlert(
        'error',
        `System error${context ? ` in ${context}` : ''}: ${error}`,
        '❌ System Error',
        {
          ...metadata,
          context,
          severity: 'high',
          timestamp: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('Error reporting system error:', error);
      throw error;
    }
  }

  /**
   * Report performance issue
   */
  async reportPerformanceIssue(
    description: string,
    metrics?: Record<string, any>
  ): Promise<void> {
    try {
      await this.createSystemAlert(
        'performance',
        description,
        '⚡ Performance Issue',
        {
          ...metrics,
          severity: 'medium',
          timestamp: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('Error reporting performance issue:', error);
      throw error;
    }
  }

  /**
   * Test notification system (Development only)
   */
  async testNotification(type: string): Promise<{ success: boolean; message: string }> {
    try {
      // Map the test type to AdminNotificationType
      const typeMap: Record<string, AdminNotificationType> = {
        'new_user': 'new_user',
        'new_listing': 'new_listing',
        'purchase': 'purchase',
        'warning': 'warning',
        'error': 'error'
      };

      const notificationType = typeMap[type] as AdminNotificationType;
      if (!notificationType) {
        throw new Error(`Invalid notification type: ${type}`);
      }

      // Create a test notification
      const testMessages: Record<string, string> = {
        'new_user': 'Test user registration notification',
        'new_listing': 'Test listing creation notification',
        'purchase': 'Test purchase completion notification',
        'warning': 'Test warning notification',
        'error': 'Test error notification'
      };

      await this.createNotification(
        notificationType,
        testMessages[type] || 'Test notification',
        undefined,
        {
          isTest: true,
          testTimestamp: new Date().toISOString()
        }
      );

      return {
        success: true,
        message: `Test ${type} notification created successfully`
      };
    } catch (error) {
      console.error('Error creating test notification:', error);
      return {
        success: false,
        message: `Failed to create test ${type} notification: ${error}`
      };
    }
  }

  /**
   * Cleanup subscriptions
   */
  cleanup(): void {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
  }
}

export default AdminNotificationService;
