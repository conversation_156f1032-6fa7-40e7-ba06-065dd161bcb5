import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  updateDoc,
  addDoc,
  writeBatch,
  Timestamp,
  orderBy,
  limit as limitQuery,
  deleteDoc,
  serverTimestamp,
  onSnapshot,
  startAfter
} from 'firebase/firestore';
import { firestore } from '../firebase/config';

/**
 * Admin metrics interface
 */
export interface AdminMetrics {
  totalUsers: number;
  activeUsers: number;
  totalListings: number;
  activeListings: number;
  totalOrders: number;
  pendingOrders: number;
  totalReports: number;
  unresolvedReports: number;
}

/**
 * Service for admin-related data operations
 */
export class AdminDataService {
  /**
   * Get all users
   */
  static async getAllUsers(): Promise<any[]> {
    try {
      const usersSnapshot = await getDocs(collection(firestore, 'users'));
      return usersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting all users:', error);
      throw error;
    }
  }

  /**
   * Get users by role
   */
  static async getUsersByRole(role: string): Promise<any[]> {
    try {
      const usersQuery = query(collection(firestore, 'users'), where('role', '==', role));
      const usersSnapshot = await getDocs(usersQuery);
      return usersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting users by role:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string): Promise<any | null> {
    try {
      const userDoc = await getDoc(doc(firestore, 'users', userId));
      if (!userDoc.exists()) {
        return null;
      }
      return {
        id: userDoc.id,
        ...userDoc.data()
      };
    } catch (error) {
      console.error('Error getting user by ID:', error);
      throw error;
    }
  }

  /**
   * Update user
   */
  static async updateUser(userId: string, userData: any): Promise<void> {
    try {
      await updateDoc(doc(firestore, 'users', userId), userData);
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Send admin message to user
   */
  static async sendAdminMessage(
    adminId: string,
    userId: string,
    message: string,
    type: 'message' | 'warning' = 'message'
  ): Promise<void> {
    try {
      // Get admin and user data
      const adminDoc = await getDoc(doc(firestore, 'users', adminId));
      const userDoc = await getDoc(doc(firestore, 'users', userId));
      
      if (!adminDoc.exists() || !userDoc.exists()) {
        throw new Error('Admin or user not found');
      }

      const adminData = adminDoc.data();

      // Create notification
      const notificationRef = collection(firestore, 'notifications');
      await addDoc(notificationRef, {
        userId,
        type: type === 'warning' ? 'admin_warning' : 'admin_message',
        senderId: adminId,
        senderName: adminData.name || 'Admin',
        title: type === 'warning' ? '⚠️ Warning from Admin' : '📢 Message from Admin',
        message,
        actionRequired: type === 'warning',
        expiresAt: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days
        createdAt: Timestamp.now(),
        dismissed: false
      });
    } catch (error) {
      console.error('Error sending admin message:', error);
      throw error;
    }
  }

  /**
   * Send broadcast notification to all users
   * Uses direct Firestore writes with proper admin authentication
   */
  static async sendBroadcastNotification(
    adminId: string,
    title: string,
    message: string,
    targetRole?: 'student' | 'merchant' | 'all'
  ): Promise<void> {
    try {
      // Get admin data
      const adminDoc = await getDoc(doc(firestore, 'users', adminId));
      if (!adminDoc.exists()) {
        throw new Error('Admin not found');
      }

      const adminData = adminDoc.data();

      // Get all users based on target role
      let usersQuery = query(collection(firestore, 'users'));
      if (targetRole && targetRole !== 'all') {
        usersQuery = query(collection(firestore, 'users'), where('role', '==', targetRole));
      }

      const usersSnapshot = await getDocs(usersQuery);
      const batch = writeBatch(firestore);
      let notificationCount = 0;

      // Create notifications for all users (except admins)
      usersSnapshot.forEach((userDoc) => {
        const userData = userDoc.data();
        if (userData.role !== 'admin') {
          const notificationRef = doc(collection(firestore, 'notifications'));
          batch.set(notificationRef, {
            userId: userDoc.id,
            type: 'admin_broadcast',
            senderId: adminId,
            senderName: adminData.name || 'Admin',
            title: `📢 ${title}`,
            message,
            actionRequired: false,
            expiresAt: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days
            createdAt: Timestamp.now(),
            dismissed: false
          });
          notificationCount++;
        }
      });

      // Commit the batch
      await batch.commit();
      console.log(`Broadcast notification sent to ${notificationCount} users`);
    } catch (error) {
      console.error('Error sending broadcast notification:', error);
      throw error;
    }
  }

  /**
   * Update order status and properties
   */
  static async updateOrder(orderId: string, updates: any): Promise<void> {
    try {
      await updateDoc(doc(firestore, 'orders', orderId), {
        ...updates,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating order:', error);
      throw error;
    }
  }

  /**
   * Get all orders
   */
  static async getAllOrders(): Promise<any[]> {
    try {
      const ordersSnapshot = await getDocs(collection(firestore, 'orders'));
      return ordersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting all orders:', error);
      throw error;
    }
  }

  /**
   * Get recent orders
   */
  static async getRecentOrders(limitCount: number = 10): Promise<any[]> {
    try {
      const ordersQuery = query(
        collection(firestore, 'orders'),
        orderBy('createdAt', 'desc'),
        limitQuery(limitCount)
      );
      const ordersSnapshot = await getDocs(ordersQuery);
      return ordersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting recent orders:', error);
      throw error;
    }
  }

  /**
   * Get all listings
   */
  static async getAllListings(): Promise<any[]> {
    try {
      const listingsSnapshot = await getDocs(collection(firestore, 'listings'));
      return listingsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting all listings:', error);
      throw error;
    }
  }

  /**
   * Get recent listings
   */
  static async getRecentListings(limitCount: number = 10): Promise<any[]> {
    try {
      const listingsQuery = query(
        collection(firestore, 'listings'),
        orderBy('createdAt', 'desc'),
        limitQuery(limitCount)
      );
      const listingsSnapshot = await getDocs(listingsQuery);
      return listingsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting recent listings:', error);
      throw error;
    }
  }

  /**
   * Get all reports
   */
  static async getAllReports(): Promise<any[]> {
    try {
      const reportsSnapshot = await getDocs(collection(firestore, 'reports'));
      return reportsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting all reports:', error);
      throw error;
    }
  }

  /**
   * Get recent reports
   */
  static async getRecentReports(limitCount: number = 10): Promise<any[]> {
    try {
      const reportsQuery = query(
        collection(firestore, 'reports'),
        orderBy('createdAt', 'desc'),
        limitQuery(limitCount)
      );
      const reportsSnapshot = await getDocs(reportsQuery);
      return reportsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting recent reports:', error);
      throw error;
    }
  }

  /**
   * Update report status
   */
  static async updateReportStatus(reportId: string, status: string): Promise<void> {
    try {
      await updateDoc(doc(firestore, 'reports', reportId), {
        status,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating report status:', error);
      throw error;
    }
  }

  /**
   * Delete report
   */
  static async deleteReport(reportId: string): Promise<void> {
    try {
      await deleteDoc(doc(firestore, 'reports', reportId));
    } catch (error) {
      console.error('Error deleting report:', error);
      throw error;
    }
  }

  /**
   * Get admin settings
   */
  static async getAdminSettings(): Promise<any | null> {
    try {
      const settingsDoc = await getDoc(doc(firestore, 'adminSettings', 'general'));
      if (!settingsDoc.exists()) {
        return null;
      }
      return {
        id: settingsDoc.id,
        ...settingsDoc.data()
      };
    } catch (error) {
      console.error('Error getting admin settings:', error);
      throw error;
    }
  }

  /**
   * Update admin settings
   */
  static async updateAdminSettings(settings: any): Promise<void> {
    try {
      await updateDoc(doc(firestore, 'adminSettings', 'general'), {
        ...settings,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating admin settings:', error);
      throw error;
    }
  }

  /**
   * Get admin metrics for dashboard
   */
  static async getMetrics(): Promise<AdminMetrics> {
    try {
      // Get all collections in parallel
      const [usersSnapshot, listingsSnapshot, ordersSnapshot, reportsSnapshot] = await Promise.all([
        getDocs(collection(firestore, 'users')),
        getDocs(collection(firestore, 'listings')),
        getDocs(collection(firestore, 'orders')),
        getDocs(collection(firestore, 'reports'))
      ]);

      // Calculate user metrics
      const users = usersSnapshot.docs.map(doc => doc.data());
      const totalUsers = users.length;
      const activeUsers = users.filter(user => user.isActive !== false).length;

      // Calculate listing metrics
      const listings = listingsSnapshot.docs.map(doc => doc.data());
      const totalListings = listings.length;
      const activeListings = listings.filter(listing =>
        listing.status === 'active' || listing.status === 'available'
      ).length;

      // Calculate order metrics
      const orders = ordersSnapshot.docs.map(doc => doc.data());
      const totalOrders = orders.length;
      const pendingOrders = orders.filter(order =>
        order.status === 'pending' || order.status === 'processing'
      ).length;

      // Calculate report metrics
      const reports = reportsSnapshot.docs.map(doc => doc.data());
      const totalReports = reports.length;
      const unresolvedReports = reports.filter(report =>
        report.status !== 'resolved' && report.status !== 'closed'
      ).length;

      return {
        totalUsers,
        activeUsers,
        totalListings,
        activeListings,
        totalOrders,
        pendingOrders,
        totalReports,
        unresolvedReports
      };
    } catch (error) {
      console.error('Error getting admin metrics:', error);
      throw error;
    }
  }

  /**
   * Subscribe to real-time admin metrics updates
   */
  static subscribeToMetrics(callback: (metrics: AdminMetrics) => void): () => void {
    try {
      // Set up listeners for all relevant collections
      const unsubscribeUsers = onSnapshot(collection(firestore, 'users'), () => {
        this.getMetrics().then(callback).catch(console.error);
      });

      const unsubscribeListings = onSnapshot(collection(firestore, 'listings'), () => {
        this.getMetrics().then(callback).catch(console.error);
      });

      const unsubscribeOrders = onSnapshot(collection(firestore, 'orders'), () => {
        this.getMetrics().then(callback).catch(console.error);
      });

      const unsubscribeReports = onSnapshot(collection(firestore, 'reports'), () => {
        this.getMetrics().then(callback).catch(console.error);
      });

      // Return cleanup function
      return () => {
        unsubscribeUsers();
        unsubscribeListings();
        unsubscribeOrders();
        unsubscribeReports();
      };
    } catch (error) {
      console.error('Error setting up metrics subscription:', error);
      // Return a no-op function if subscription fails
      return () => {};
    }
  }

  /**
   * Get users with pagination and filtering (for AdminUsers component)
   */
  static async getUsers(options?: { pageSize?: number; lastDoc?: any }, filters?: any): Promise<{
    users: any[];
    hasMore: boolean;
    lastDoc: any;
  }> {
    try {
      const pageSize = options?.pageSize || 20;
      let usersQuery = query(collection(firestore, 'users'), orderBy('createdAt', 'desc'));

      // Apply filters
      if (filters?.role) {
        usersQuery = query(usersQuery, where('role', '==', filters.role));
      }
      if (filters?.university) {
        usersQuery = query(usersQuery, where('university', '==', filters.university));
      }
      if (filters?.status) {
        usersQuery = query(usersQuery, where('status', '==', filters.status));
      }

      // Add pagination
      if (options?.lastDoc) {
        usersQuery = query(usersQuery, startAfter(options.lastDoc));
      }
      usersQuery = query(usersQuery, limitQuery(pageSize + 1));

      const snapshot = await getDocs(usersQuery);
      const users = snapshot.docs.slice(0, pageSize).map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return {
        users,
        hasMore: snapshot.docs.length > pageSize,
        lastDoc: snapshot.docs[pageSize - 1] || null
      };
    } catch (error) {
      console.error('Error getting users:', error);
      throw error;
    }
  }

  /**
   * Get listings with pagination and filtering (for AdminListings component)
   */
  static async getListings(options?: { pageSize?: number; lastDoc?: any }, filters?: any): Promise<{
    listings: any[];
    hasMore: boolean;
    lastDoc: any;
  }> {
    try {
      const pageSize = options?.pageSize || 20;
      let listingsQuery = query(collection(firestore, 'listings'), orderBy('createdAt', 'desc'));

      // Apply filters
      if (filters?.status) {
        listingsQuery = query(listingsQuery, where('status', '==', filters.status));
      }
      if (filters?.category) {
        listingsQuery = query(listingsQuery, where('category', '==', filters.category));
      }
      if (filters?.university) {
        listingsQuery = query(listingsQuery, where('university', '==', filters.university));
      }

      // Add pagination
      if (options?.lastDoc) {
        listingsQuery = query(listingsQuery, startAfter(options.lastDoc));
      }
      listingsQuery = query(listingsQuery, limitQuery(pageSize + 1));

      const snapshot = await getDocs(listingsQuery);
      const listings = snapshot.docs.slice(0, pageSize).map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return {
        listings,
        hasMore: snapshot.docs.length > pageSize,
        lastDoc: snapshot.docs[pageSize - 1] || null
      };
    } catch (error) {
      console.error('Error getting listings:', error);
      throw error;
    }
  }

  /**
   * Delete listing
   */
  static async deleteListing(listingId: string): Promise<void> {
    try {
      await deleteDoc(doc(firestore, 'listings', listingId));
    } catch (error) {
      console.error('Error deleting listing:', error);
      throw error;
    }
  }

  /**
   * Update listing status
   */
  static async updateListingStatus(listingId: string, status: string): Promise<void> {
    try {
      await updateDoc(doc(firestore, 'listings', listingId), {
        status,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating listing status:', error);
      throw error;
    }
  }

  /**
   * Delete user
   */
  static async deleteUser(userId: string): Promise<void> {
    try {
      await deleteDoc(doc(firestore, 'users', userId));
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Update user status
   */
  static async updateUserStatus(userId: string, status: string): Promise<void> {
    try {
      await updateDoc(doc(firestore, 'users', userId), {
        status,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating user status:', error);
      throw error;
    }
  }
}
