// Simple API tester for ReeFlex models
import axios from 'axios';

export class ApiTester {
  static async testOpenAI(apiKey: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: 'gpt-4o-mini',
          messages: [{ role: 'user', content: 'Hello, this is a test.' }],
          max_tokens: 10
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          timeout: 10000
        }
      );
      
      return { success: true };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.response?.status ? `HTTP ${error.response.status}: ${error.response.data?.error?.message || error.message}` : error.message 
      };
    }
  }

  static async testGemini(apiKey: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await axios.post(
        `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
        {
          contents: [{
            parts: [{ text: 'Hello, this is a test.' }]
          }],
          generationConfig: {
            maxOutputTokens: 10
          }
        },
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );
      
      return { success: true };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.response?.status ? `HTTP ${error.response.status}: ${error.response.data?.error?.message || error.message}` : error.message 
      };
    }
  }

  static async testOpenRouter(apiKey: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await axios.post(
        'https://openrouter.ai/api/v1/chat/completions',
        {
          model: 'anthropic/claude-3-haiku',
          messages: [{ role: 'user', content: 'Hello, this is a test.' }],
          max_tokens: 10,
          route: 'fallback'
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': 'https://hivecampus.app',
            'X-Title': 'Hive Campus ReeFlex AI'
          },
          timeout: 10000
        }
      );

      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.status ? `HTTP ${error.response.status}: ${error.response.data?.error?.message || error.message}` : error.message
      };
    }
  }

  static async testAllApis(): Promise<{
    openai: { success: boolean; error?: string };
    gemini: { success: boolean; error?: string };
    openrouter: { success: boolean; error?: string };
  }> {
    const openaiKey = import.meta.env.VITE_OPENAI_API_KEY;
    const geminiKey = import.meta.env.VITE_GEMINI_API_KEY;
    const openrouterKey = import.meta.env.VITE_OPENROUTER_API_KEY;

    const [openaiResult, geminiResult, openrouterResult] = await Promise.all([
      openaiKey ? this.testOpenAI(openaiKey) : { success: false, error: 'No API key configured' },
      geminiKey ? this.testGemini(geminiKey) : { success: false, error: 'No API key configured' },
      openrouterKey ? this.testOpenRouter(openrouterKey) : { success: false, error: 'No API key configured' }
    ]);

    return {
      openai: openaiResult,
      gemini: geminiResult,
      openrouter: openrouterResult
    };
  }
}
