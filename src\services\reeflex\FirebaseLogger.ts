import { 
  collection, 
  addDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit, 
  Timestamp,
  doc,
  getDoc
} from 'firebase/firestore';
import { db } from '../../firebase/config';
import { 
  ReeFlexConversation, 
  ReeFlexMessage, 
  ReeFlexInsight,
  FirebaseMetrics,
  StripeMetrics,
  AppMetrics
} from './types';

export class FirebaseLogger {
  private static instance: FirebaseLogger;

  static getInstance(): FirebaseLogger {
    if (!FirebaseLogger.instance) {
      FirebaseLogger.instance = new FirebaseLogger();
    }
    return FirebaseLogger.instance;
  }

  async logConversation(conversation: Omit<ReeFlexConversation, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'reeflex_logs'), {
        ...conversation,
        createdAt: Timestamp.fromDate(conversation.createdAt),
        updatedAt: Timestamp.fromDate(conversation.updatedAt)
      });
      
      return docRef.id;
    } catch (error) {
      console.error('Error logging conversation:', error);
      throw error;
    }
  }

  async logMessage(conversationId: string, message: ReeFlexMessage): Promise<void> {
    try {
      await addDoc(collection(db, `reeflex_logs/${conversationId}/messages`), {
        ...message,
        timestamp: Timestamp.fromDate(message.timestamp)
      });
    } catch (error) {
      console.error('Error logging message:', error);
      throw error;
    }
  }

  async logInsight(insight: Omit<ReeFlexInsight, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'reeflex_insights'), {
        ...insight,
        timestamp: Timestamp.fromDate(insight.timestamp)
      });
      
      return docRef.id;
    } catch (error) {
      console.error('Error logging insight:', error);
      throw error;
    }
  }

  async getRecentErrors(limitCount = 10): Promise<any[]> {
    try {
      // Skip complex queries that require indexes for now
      console.log('Skipping recent errors query to avoid index requirements');
      return [];
    } catch (error) {
      console.error('Error fetching recent errors:', error);
      return [];
    }
  }

  async getPerformanceIssues(limitCount = 5): Promise<any[]> {
    try {
      // Skip complex queries that require indexes for now
      console.log('Skipping performance issues query to avoid index requirements');
      return [];
    } catch (error) {
      console.error('Error fetching performance issues:', error);
      return [];
    }
  }

  async getUserFeedback(limitCount = 10): Promise<any[]> {
    try {
      const feedbackRef = collection(db, 'feedback');
      const q = query(
        feedbackRef,
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching user feedback:', error);
      return [];
    }
  }

  async getFirebaseMetrics(): Promise<FirebaseMetrics> {
    try {
      // Get user metrics
      const usersSnapshot = await getDocs(collection(db, 'users'));
      const totalUsers = usersSnapshot.size;

      // Skip complex queries that require indexes for now
      const newSignups = 0; // Would need index
      const errorCount = 0; // Would need index

      return {
        auth: {
          totalUsers,
          activeUsers: Math.floor(totalUsers * 0.7), // Estimate
          newSignups,
          failedLogins: 0 // Would need to track this separately
        },
        firestore: {
          reads: 0, // Would need Firebase Analytics
          writes: 0,
          deletes: 0,
          errors: errorCount
        },
        storage: {
          uploads: 0, // Would need to track this
          downloads: 0,
          errors: 0
        },
        functions: {
          invocations: 0, // Would need Firebase Analytics
          errors: 0,
          duration: 0
        }
      };
    } catch (error) {
      console.error('Error getting Firebase metrics:', error);
      return {
        auth: { totalUsers: 0, activeUsers: 0, newSignups: 0, failedLogins: 0 },
        firestore: { reads: 0, writes: 0, deletes: 0, errors: 0 },
        storage: { uploads: 0, downloads: 0, errors: 0 },
        functions: { invocations: 0, errors: 0, duration: 0 }
      };
    }
  }

  async getAppMetrics(): Promise<AppMetrics> {
    try {
      // Get basic metrics without complex filtering to avoid permission issues
      let totalListings = 0;
      let totalMessages = 0;

      try {
        const listingsSnapshot = await getDocs(collection(db, 'listings'));
        totalListings = listingsSnapshot.size;
      } catch (error) {
        console.log('Could not access listings collection:', error);
      }

      try {
        const messagesSnapshot = await getDocs(collection(db, 'chats'));
        totalMessages = messagesSnapshot.size;
      } catch (error) {
        console.log('Could not access chats collection:', error);
      }

      // Use basic metrics for now
      const ordersMetrics = {
        total: 0,
        pending: 0,
        completed: 0,
        cancelled: 0
      };

      return {
        listings: {
          total: totalListings,
          active: Math.floor(totalListings * 0.8), // Estimate
          sold: Math.floor(totalListings * 0.2), // Estimate
          views: 0 // Would need to track this separately
        },
        messages: {
          total: totalMessages,
          unread: 0 // Would need to track this separately
        },
        orders: ordersMetrics
      };
    } catch (error) {
      console.error('Error getting app metrics:', error);
      return {
        listings: { total: 0, active: 0, sold: 0, views: 0 },
        messages: { total: 0, unread: 0 },
        orders: { total: 0, pending: 0, completed: 0, cancelled: 0 }
      };
    }
  }

  async getSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    uptime: number;
  }> {
    try {
      const issues: string[] = [];
      
      // Check for recent errors
      const recentErrors = await this.getRecentErrors(5);
      if (recentErrors.length > 3) {
        issues.push(`High error rate: ${recentErrors.length} errors in recent activity`);
      }

      // Check for performance issues
      const performanceIssues = await this.getPerformanceIssues(3);
      if (performanceIssues.length > 0) {
        issues.push(`Performance issues detected: ${performanceIssues.length} slow operations`);
      }

      // Determine overall status
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (issues.length > 0) {
        status = issues.length > 2 ? 'critical' : 'warning';
      }

      return {
        status,
        issues,
        uptime: Date.now() - new Date('2024-01-01').getTime() // Placeholder uptime
      };
    } catch (error) {
      console.error('Error getting system health:', error);
      return {
        status: 'critical',
        issues: ['Unable to determine system health'],
        uptime: 0
      };
    }
  }
}
