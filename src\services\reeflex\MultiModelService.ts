
import {
  MultiModelRequest,
  MultiModelResponse,
  ModelResponse,
  ReeFlexContext
} from './types';
import { functions } from '../../firebase/config';
import { httpsCallable } from 'firebase/functions';
import { FirebaseLogger } from './FirebaseLogger';

export class MultiModelService {
  private firebaseLogger: FirebaseLogger;

  constructor() {
    this.initializeServices();
    this.firebaseLogger = FirebaseLogger.getInstance();
  }

  private initializeServices() {
    // Services are now handled via Firebase Functions for security
    // No need to initialize client-side API services
    console.log('ReeFlex MultiModelService initialized - using Firebase Functions for AI calls');
  }

  async processMultiModelRequest(request: MultiModelRequest): Promise<MultiModelResponse> {
    try {
      // Use Firebase Functions for secure API calls
      return await this.processMultiModelRequestViaFirebase(request);
    } catch (error) {
      console.error('Multi-model request failed:', error);
      // Return a fallback response instead of throwing
      return {
        responses: [{
          id: 'error',
          model: 'system',
          content: '',
          latency: 0,
          error: 'AI services are temporarily unavailable. Please try again later.',
          timestamp: new Date()
        }],
        verdict: 'AI services are currently experiencing issues. The system is working to resolve this.',
        totalLatency: 0,
        timestamp: new Date()
      };
    }
  }

  private async processMultiModelRequestViaFirebase(request: MultiModelRequest): Promise<MultiModelResponse> {
    try {
      const reeflexChatFunction = httpsCallable(functions, 'reeflexChat');
      const result = await reeflexChatFunction({
        prompt: request.prompt,
        models: request.models,
        temperature: request.temperature,
        maxTokens: request.maxTokens,
        context: request.context
      });

      const data = result.data as any;

      return {
        responses: data.responses || [],
        verdict: data.verdict || 'ReeFlex system response',
        requestId: `firebase-${Date.now()}`,
        timestamp: new Date(),
        totalLatency: data.latency || 100
      };
    } catch (error) {
      console.error('Firebase function call failed:', error);
      throw error;
    }
  }



  private createReeFlexSystemPrompt(context?: ReeFlexContext): string {
    return `You are ReeFlex — a multi-agent embedded AI engineering system that lives silently inside the Hive Campus codebase. You are not one model, but part of a team of three AI models (OpenAI, Google Gemini, and DeepSeek) working together as a senior AI engineering team.

Your role is to constantly observe, diagnose, and improve Hive Campus - a student marketplace application built with React, TypeScript, Firebase, and Stripe.

You have access to real-time data about:
- Firebase Authentication, Firestore, Storage, and Functions
- Stripe payments, escrow, and Connect integration
- User behavior, errors, and performance metrics
- Shipping integration with Shippo
- Real-time chat and messaging systems
- Admin dashboard and analytics

When responding:
1. Be technical but accessible
2. Provide actionable insights
3. Consider the full stack (frontend, backend, payments, shipping)
4. Think like a senior engineer who understands both code and business
5. Prioritize user experience and platform stability
6. Consider scalability and security implications

Current context: ${context ? JSON.stringify(context, null, 2) : 'No specific context provided'}`;
  }

  private async enhancePromptWithContext(prompt: string, context?: ReeFlexContext): Promise<string> {
    if (!context) return prompt;

    let enhancedPrompt = prompt;

    // Add recent errors if available
    if (context.recentErrors && context.recentErrors.length > 0) {
      enhancedPrompt += `\n\nRecent Errors:\n${context.recentErrors.slice(0, 5).map(error => 
        `- ${error.type}: ${error.message} (${error.route})`
      ).join('\n')}`;
    }

    // Add performance context
    if (context.performanceMetrics) {
      enhancedPrompt += `\n\nPerformance Context:\n${JSON.stringify(context.performanceMetrics, null, 2)}`;
    }

    // Add user role context
    if (context.userRole) {
      enhancedPrompt += `\n\nUser Role: ${context.userRole}`;
    }

    return enhancedPrompt;
  }

  private async generateVerdict(responses: ModelResponse[], originalPrompt: string): Promise<string> {
    const validResponses = responses.filter(r => !r.error && r.content.trim().length > 0);
    const serviceUnavailableResponses = responses.filter(r => r.id.includes('unavailable'));

    if (validResponses.length === 0 && serviceUnavailableResponses.length === 0) {
      return "❌ All models failed to generate responses. Please check API configurations and try again.";
    }

    // Include service unavailable responses as valid for display purposes
    const allUsableResponses = [...validResponses, ...serviceUnavailableResponses];

    if (allUsableResponses.length === 1) {
      return `🧠 **ReeFlex Verdict** (Single Model Response)\n\n${allUsableResponses[0].content}`;
    }

    // Synthesize multiple responses
    const synthesis = this.synthesizeResponses(allUsableResponses, originalPrompt);
    return `🧠 **ReeFlex Verdict** (Synthesized from ${allUsableResponses.length} models)\n\n${synthesis}`;
  }

  private synthesizeResponses(responses: ModelResponse[], originalPrompt: string): string {
    // Simple synthesis logic - can be enhanced with AI-powered synthesis
    const commonThemes: string[] = [];
    const uniqueInsights: string[] = [];
    
    // Extract key points from each response
    responses.forEach((response, index) => {
      const modelName = response.model.includes('gpt') ? 'OpenAI' :
                       response.model.includes('gemini') ? 'Gemini' :
                       response.model.includes('claude') || response.model.includes('llama') || response.model.includes('mixtral') ? 'OpenRouter' : 'AI Model';

      uniqueInsights.push(`**${modelName}**: ${response.content.substring(0, 200)}...`);
    });

    return `Based on analysis from ${responses.length} AI models:\n\n${uniqueInsights.join('\n\n')}\n\n**Consensus**: The models agree on the need for immediate attention to the issues raised. Consider implementing the most frequently mentioned recommendations first.`;
  }

  async gatherSystemContext(): Promise<ReeFlexContext> {
    try {
      // Use client-side context gathering for now
      // Gather basic metrics without complex queries that require indexes
      const firebaseData = await this.firebaseLogger.getFirebaseMetrics();
      const appMetrics = await this.firebaseLogger.getAppMetrics();
      const systemHealth = await this.firebaseLogger.getSystemHealth();

      return {
        recentErrors: [], // Skip for now to avoid index issues
        performanceMetrics: {
          issues: [],
          systemHealth
        },
        firebaseData: {
          ...firebaseData,
          appMetrics
        }
      };
    } catch (error) {
      console.error('Error gathering system context:', error);
      return {};
    }
  }

  async logConversation(
    adminId: string,
    prompt: string,
    response: MultiModelResponse
  ): Promise<void> {
    try {
      // Skip logging for now to avoid permission issues
      console.log('Conversation logged locally:', { adminId, prompt: prompt.substring(0, 100), responseLength: response.verdict.length });
    } catch (error) {
      console.error('Error logging conversation:', error);
    }
  }
}
