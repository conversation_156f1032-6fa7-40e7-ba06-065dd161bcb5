import axios from 'axios';
import { ModelResponse } from '../types';

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
      role: string;
    };
    finishReason: string;
    index: number;
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  promptFeedback: {
    safetyRatings: Array<{
      category: string;
      probability: string;
    }>;
  };
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

export class GeminiService {
  private apiKey: string;
  private baseURL = 'https://generativelanguage.googleapis.com/v1';
  private timeout = 30000; // 30 seconds
  private retries = 3;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateResponse(
    prompt: string,
    systemPrompt?: string,
    temperature = 0.7,
    maxTokens = 2000
  ): Promise<ModelResponse> {
    const startTime = Date.now();
    
    try {
      const response = await this.makeRequest(prompt, systemPrompt, temperature, maxTokens);
      const latency = Date.now() - startTime;
      
      const content = response.candidates[0]?.content?.parts[0]?.text || '';
      
      return {
        id: `gemini-${Date.now()}`,
        model: 'gemini-1.5-flash',
        content,
        latency,
        tokens: {
          prompt: response.usageMetadata.promptTokenCount,
          completion: response.usageMetadata.candidatesTokenCount,
          total: response.usageMetadata.totalTokenCount
        },
        timestamp: new Date()
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        id: 'error',
        model: 'gemini-1.5-flash',
        content: '',
        latency,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      };
    }
  }

  private async makeRequest(
    prompt: string,
    systemPrompt?: string,
    temperature = 0.7,
    maxTokens = 2000,
    attempt = 1
  ): Promise<GeminiResponse> {
    try {
      // Combine system prompt and user prompt for Gemini
      const fullPrompt = systemPrompt 
        ? `${systemPrompt}\n\nUser: ${prompt}`
        : prompt;

      const response = await axios.post<GeminiResponse>(
        `${this.baseURL}/models/gemini-1.5-flash:generateContent?key=${this.apiKey}`,
        {
          contents: [{
            parts: [{
              text: fullPrompt
            }]
          }],
          generationConfig: {
            temperature,
            maxOutputTokens: maxTokens,
            topP: 0.8,
            topK: 10
          }
        },
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: this.timeout
        }
      );

      return response.data;
    } catch (error) {
      console.error('Gemini API Error:', error);
      if (attempt < this.retries) {
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.makeRequest(prompt, systemPrompt, temperature, maxTokens, attempt + 1);
      }

      throw error;
    }
  }
}
