import axios from 'axios';
import { ModelResponse } from '../types';

interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class OpenAIService {
  private apiKey: string;
  private baseURL = 'https://api.openai.com/v1';
  private timeout = 30000; // 30 seconds
  private retries = 3;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateResponse(
    prompt: string,
    systemPrompt?: string,
    temperature = 0.7,
    maxTokens = 2000
  ): Promise<ModelResponse> {
    const startTime = Date.now();

    try {
      const response = await this.makeRequest(prompt, systemPrompt, temperature, maxTokens);
      const latency = Date.now() - startTime;

      return {
        id: response.id,
        model: response.model,
        content: response.choices[0].message.content,
        latency,
        tokens: {
          prompt: response.usage.prompt_tokens,
          completion: response.usage.completion_tokens,
          total: response.usage.total_tokens
        },
        timestamp: new Date()
      };
    } catch (error) {
      const latency = Date.now() - startTime;

      return {
        id: 'error',
        model: 'gpt-4',
        content: '',
        latency,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      };
    }
  }

  private async makeRequest(
    prompt: string,
    systemPrompt?: string,
    temperature = 0.7,
    maxTokens = 2000,
    attempt = 1
  ): Promise<OpenAIResponse> {
    try {
      const messages = [];

      if (systemPrompt) {
        messages.push({
          role: 'system',
          content: systemPrompt
        });
      }

      messages.push({
        role: 'user',
        content: prompt
      });

      const response = await axios.post<OpenAIResponse>(
        `${this.baseURL}/chat/completions`,
        {
          model: 'gpt-4o-mini', // Use gpt-4o-mini which is more reliable and cheaper
          messages,
          temperature,
          max_tokens: maxTokens
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          timeout: this.timeout
        }
      );

      return response.data;
    } catch (error) {
      console.error('OpenAI API Error:', error);
      if (attempt < this.retries) {
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.makeRequest(prompt, systemPrompt, temperature, maxTokens, attempt + 1);
      }

      throw error;
    }
  }
}
