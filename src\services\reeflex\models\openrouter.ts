import axios from 'axios';
import { ModelResponse } from '../types';

interface OpenRouterResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class OpenRouterService {
  private apiKey: string;
  private baseURL = 'https://openrouter.ai/api/v1';
  private timeout = 30000; // 30 seconds
  private retries = 3;
  private defaultModel = 'anthropic/claude-3-haiku'; // Default to Claude 3 Haiku

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateResponse(
    prompt: string,
    systemPrompt?: string,
    temperature = 0.7,
    maxTokens = 2000,
    model?: string
  ): Promise<ModelResponse> {
    const startTime = Date.now();
    const selectedModel = model || this.defaultModel;

    try {
      const response = await this.makeRequest(prompt, systemPrompt, temperature, maxTokens, selectedModel);
      const latency = Date.now() - startTime;

      return {
        id: response.id,
        model: response.model,
        content: response.choices[0].message.content,
        latency,
        tokens: {
          prompt: response.usage.prompt_tokens,
          completion: response.usage.completion_tokens,
          total: response.usage.total_tokens
        },
        timestamp: new Date()
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      console.error('OpenRouter API Error:', error);

      // Check if it's a payment/auth error (402, 401, 403)
      if (error instanceof Error && (error.message.includes('402') || error.message.includes('401') || error.message.includes('403'))) {
        return {
          id: 'openrouter-unavailable',
          model: selectedModel,
          content: '🤖 **OpenRouter Analysis** (Service Temporarily Unavailable)\n\nOpenRouter API is currently unavailable due to authentication or billing issues. The other AI models (OpenAI and Gemini) are still providing analysis.\n\n**Available Models**: Claude 3 Haiku, LLaMA 3, Mixtral, and more\n**Note**: To restore OpenRouter functionality, please check your API key and billing status at https://openrouter.ai/',
          latency,
          tokens: {
            prompt: 0,
            completion: 0,
            total: 0
          },
          timestamp: new Date()
        };
      }

      return {
        id: 'error',
        model: selectedModel,
        content: '',
        latency,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      };
    }
  }

  private async makeRequest(
    prompt: string,
    systemPrompt?: string,
    temperature = 0.7,
    maxTokens = 2000,
    model = this.defaultModel,
    attempt = 1
  ): Promise<OpenRouterResponse> {
    try {
      const messages = [];
      
      if (systemPrompt) {
        messages.push({
          role: 'system',
          content: systemPrompt
        });
      }
      
      messages.push({
        role: 'user',
        content: prompt
      });

      const response = await axios.post<OpenRouterResponse>(
        `${this.baseURL}/chat/completions`,
        {
          model,
          messages,
          temperature,
          max_tokens: maxTokens,
          // OpenRouter specific headers for better routing
          route: 'fallback'
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
            'HTTP-Referer': 'https://hivecampus.app', // Required by OpenRouter
            'X-Title': 'Hive Campus ReeFlex AI' // Optional but recommended
          },
          timeout: this.timeout
        }
      );

      return response.data;
    } catch (error) {
      console.error('OpenRouter API Request Error:', error);
      if (attempt < this.retries) {
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.makeRequest(prompt, systemPrompt, temperature, maxTokens, model, attempt + 1);
      }

      throw error;
    }
  }

  // Method to get available models
  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await axios.get(`${this.baseURL}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });
      
      return response.data.data.map((model: any) => model.id);
    } catch (error) {
      console.error('Error fetching OpenRouter models:', error);
      // Return default models if API call fails
      return [
        'anthropic/claude-3-haiku',
        'meta-llama/llama-3-8b-instruct',
        'mistralai/mixtral-8x7b-instruct',
        'openai/gpt-3.5-turbo',
        'google/gemma-7b-it'
      ];
    }
  }
}
