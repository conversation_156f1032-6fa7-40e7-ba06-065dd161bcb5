// ReeFlex Multi-Model AI Service Types

export interface ModelResponse {
  id: string;
  model: string;
  content: string;
  latency: number;
  tokens?: {
    prompt: number;
    completion: number;
    total: number;
  };
  error?: string;
  timestamp: Date;
}

export interface MultiModelRequest {
  prompt: string;
  systemPrompt?: string;
  models: AIModel[];
  temperature?: number;
  maxTokens?: number;
  context?: ReeFlexContext;
}

export interface MultiModelResponse {
  responses: ModelResponse[];
  verdict: string;
  requestId: string;
  timestamp: Date;
  totalLatency: number;
}

export interface ReeFlexContext {
  userId?: string;
  sessionId?: string;
  route?: string;
  userRole?: 'admin' | 'student' | 'merchant';
  recentErrors?: any[];
  performanceMetrics?: any;
  stripeData?: any;
  firebaseData?: any;
}

export type AIModel = 'openai' | 'gemini' | 'openrouter';

export interface ModelConfig {
  enabled: boolean;
  apiKey: string;
  endpoint: string;
  model: string;
  timeout: number;
  retries: number;
}

export interface ReeFlexInsight {
  id: string;
  type: 'bug' | 'performance' | 'ux' | 'security' | 'growth' | 'feature';
  severity: 'critical' | 'high' | 'medium' | 'low';
  title: string;
  description: string;
  recommendation: string;
  confidence: number;
  relatedData: any;
  timestamp: Date;
}

export interface ReeFlexConversation {
  id: string;
  adminId: string;
  messages: ReeFlexMessage[];
  insights: ReeFlexInsight[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ReeFlexMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  models?: ModelResponse[];
  verdict?: string;
  timestamp: Date;
}

export interface FirebaseMetrics {
  auth: {
    totalUsers: number;
    activeUsers: number;
    newSignups: number;
    failedLogins: number;
  };
  firestore: {
    reads: number;
    writes: number;
    deletes: number;
    errors: number;
  };
  storage: {
    uploads: number;
    downloads: number;
    errors: number;
  };
  functions: {
    invocations: number;
    errors: number;
    duration: number;
  };
}

export interface StripeMetrics {
  payments: {
    successful: number;
    failed: number;
    totalAmount: number;
    averageAmount: number;
  };
  disputes: number;
  refunds: number;
  webhookErrors: number;
}

export interface AppMetrics {
  listings: {
    total: number;
    active: number;
    sold: number;
    views: number;
  };
  messages: {
    total: number;
    unread: number;
  };
  orders: {
    total: number;
    pending: number;
    completed: number;
    cancelled: number;
  };
}
