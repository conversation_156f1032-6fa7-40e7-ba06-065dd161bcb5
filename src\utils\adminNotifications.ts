import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';
import { AdminNotificationType } from '../firebase/types';

// Firebase Functions callable references
const createWarningNotificationFn = httpsCallable(functions, 'createWarningNotification');
const createSystemAlertFn = httpsCallable(functions, 'createSystemAlert');

/**
 * Utility class for creating admin notifications from the frontend
 */
export class AdminNotificationUtils {
  /**
   * Create a warning notification
   */
  static async createWarning(
    message: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await createWarningNotificationFn({
        message,
        severity,
        metadata
      });
    } catch (error) {
      console.error('Error creating warning notification:', error);
      throw error;
    }
  }

  /**
   * Create a system alert notification
   */
  static async createSystemAlert(
    type: AdminNotificationType,
    message: string,
    title?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await createSystemAlertFn({
        type,
        message,
        title,
        metadata
      });
    } catch (error) {
      console.error('Error creating system alert:', error);
      throw error;
    }
  }

  /**
   * Report suspicious activity
   */
  static async reportSuspiciousActivity(
    userId: string,
    activity: string,
    details?: Record<string, any>
  ): Promise<void> {
    return this.createSystemAlert(
      'suspicious_activity',
      `Suspicious activity detected: ${activity}`,
      '🚨 Suspicious Activity Alert',
      {
        userId,
        activity,
        severity: 'high',
        ...details
      }
    );
  }

  /**
   * Report payment failure
   */
  static async reportPaymentFailure(
    orderId: string,
    userId: string,
    amount: number,
    error: string
  ): Promise<void> {
    return this.createSystemAlert(
      'payment_failed',
      `Payment failed for order ${orderId}: ${error}`,
      '💸 Payment Failure',
      {
        orderId,
        userId,
        amount,
        error,
        severity: 'high'
      }
    );
  }

  /**
   * Report Stripe webhook issues
   */
  static async reportStripeWebhookError(
    webhookId: string,
    eventType: string,
    error: string
  ): Promise<void> {
    return this.createSystemAlert(
      'stripe_webhook',
      `Stripe webhook error for ${eventType}: ${error}`,
      '💳 Stripe Webhook Error',
      {
        webhookId,
        eventType,
        error,
        severity: 'high'
      }
    );
  }

  /**
   * Report listing flagged by users
   */
  static async reportListingFlagged(
    listingId: string,
    listingTitle: string,
    reportedBy: string,
    reason: string
  ): Promise<void> {
    return this.createSystemAlert(
      'listing_flagged',
      `Listing "${listingTitle}" has been flagged: ${reason}`,
      '🚩 Listing Flagged',
      {
        listingId,
        listingTitle,
        reportedBy,
        reason,
        severity: 'medium'
      }
    );
  }

  /**
   * Report user blocked/suspended
   */
  static async reportUserBlocked(
    userId: string,
    userName: string,
    reason: string,
    blockedBy: string
  ): Promise<void> {
    return this.createSystemAlert(
      'user_blocked',
      `User ${userName} has been blocked: ${reason}`,
      '🚫 User Blocked',
      {
        userId,
        userName,
        reason,
        blockedBy,
        severity: 'medium'
      }
    );
  }

  /**
   * Report system errors
   */
  static async reportSystemError(
    error: string,
    context?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.createSystemAlert(
      'error',
      `System error${context ? ` in ${context}` : ''}: ${error}`,
      '❌ System Error',
      {
        error,
        context,
        severity: 'high',
        ...metadata
      }
    );
  }

  /**
   * Report performance issues
   */
  static async reportPerformanceIssue(
    metric: string,
    value: number,
    threshold: number,
    context?: string
  ): Promise<void> {
    return this.createSystemAlert(
      'performance',
      `Performance issue: ${metric} is ${value} (threshold: ${threshold})${context ? ` in ${context}` : ''}`,
      '⚡ Performance Alert',
      {
        metric,
        value,
        threshold,
        context,
        severity: value > threshold * 2 ? 'high' : 'medium'
      }
    );
  }

  /**
   * Report security issues
   */
  static async reportSecurityIssue(
    issue: string,
    userId?: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'high',
    metadata?: Record<string, any>
  ): Promise<void> {
    return this.createSystemAlert(
      'security',
      `Security issue detected: ${issue}`,
      '🔒 Security Alert',
      {
        issue,
        userId,
        severity,
        ...metadata
      }
    );
  }

  /**
   * Report maintenance notifications
   */
  static async reportMaintenance(
    message: string,
    scheduledTime?: Date,
    duration?: string
  ): Promise<void> {
    return this.createSystemAlert(
      'maintenance',
      message,
      '🔧 Maintenance Notice',
      {
        scheduledTime: scheduledTime?.toISOString(),
        duration,
        severity: 'low'
      }
    );
  }
}

/**
 * Quick access functions for common notifications
 */

export const notifyAdminWarning = AdminNotificationUtils.createWarning;
export const notifySystemAlert = AdminNotificationUtils.createSystemAlert;
export const reportSuspiciousActivity = AdminNotificationUtils.reportSuspiciousActivity;
export const reportPaymentFailure = AdminNotificationUtils.reportPaymentFailure;
export const reportStripeWebhookError = AdminNotificationUtils.reportStripeWebhookError;
export const reportListingFlagged = AdminNotificationUtils.reportListingFlagged;
export const reportUserBlocked = AdminNotificationUtils.reportUserBlocked;
export const reportSystemError = AdminNotificationUtils.reportSystemError;
export const reportPerformanceIssue = AdminNotificationUtils.reportPerformanceIssue;
export const reportSecurityIssue = AdminNotificationUtils.reportSecurityIssue;
export const reportMaintenance = AdminNotificationUtils.reportMaintenance;

export default AdminNotificationUtils;
