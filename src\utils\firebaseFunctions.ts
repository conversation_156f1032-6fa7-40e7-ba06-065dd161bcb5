import { auth } from '../firebase/config';

const FUNCTIONS_BASE_URL = 'https://us-central1-h1c1-798a8.cloudfunctions.net';

// Helper function to make authenticated requests to Firebase Functions
export const callFirebaseFunction = async (functionName: string, data: any) => {
  const user = auth.currentUser;
  if (!user) {
    throw new Error('User not authenticated');
  }

  const token = await user.getIdToken();
  
  const response = await fetch(`${FUNCTIONS_BASE_URL}/${functionName}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `HTTP ${response.status}`);
  }

  return await response.json();
};

// Specific function wrappers
export const verifyAdminPin = async (pin: string) => {
  return await callFirebaseFunction('verifyAdminPin', { pin });
};

export const setAdminPin = async (pin: string) => {
  return await callFirebaseFunction('setAdminPin', { pin });
};

export const getWalletData = async () => {
  return await callFirebaseFunction('getWalletData', {});
};

export const getStripeConnectAccountStatus = async () => {
  return await callFirebaseFunction('getStripeConnectAccountStatus', {});
};

export const getSellerPendingPayouts = async () => {
  return await callFirebaseFunction('getSellerPendingPayouts', {});
};
