<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Notification Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Admin Notification System Test</h1>
        <p>This page tests the admin notification system to ensure it works without CSP violations.</p>
        
        <div class="test-section">
            <h3>1. CSP Violation Check</h3>
            <p>Monitoring for Content Security Policy violations...</p>
            <div id="csp-status" class="status info">
                ✅ No CSP violations detected yet. This is good!
            </div>
            <div id="csp-violations"></div>
        </div>

        <div class="test-section">
            <h3>2. Firebase Connection Test</h3>
            <button onclick="testFirebaseConnection()">Test Firebase Connection</button>
            <div id="firebase-status"></div>
        </div>

        <div class="test-section">
            <h3>3. ReeFlex Function Test</h3>
            <button onclick="testReeflexFunction()">Test ReeFlex Function</button>
            <div id="reeflex-status"></div>
        </div>

        <div class="test-section">
            <h3>4. Admin Notification Test</h3>
            <p>This simulates sending a broadcast notification (requires admin authentication):</p>
            <button onclick="testAdminNotification()" id="notification-btn">Test Broadcast Notification</button>
            <div id="notification-status"></div>
        </div>

        <div class="test-section">
            <h3>5. Network Requests Monitor</h3>
            <p>Monitoring all network requests to detect any blocked API calls:</p>
            <div id="network-status" class="status info">
                📡 Monitoring network requests...
            </div>
            <div id="network-log"></div>
        </div>
    </div>

    <script type="module">
        // Monitor CSP violations
        document.addEventListener('securitypolicyviolation', (e) => {
            console.error('CSP Violation:', e);
            const cspStatus = document.getElementById('csp-status');
            const cspViolations = document.getElementById('csp-violations');
            
            cspStatus.className = 'status error';
            cspStatus.innerHTML = '❌ CSP Violation Detected!';
            
            const violation = document.createElement('div');
            violation.className = 'status error';
            violation.innerHTML = `
                <strong>Blocked URI:</strong> ${e.blockedURI}<br>
                <strong>Violated Directive:</strong> ${e.violatedDirective}<br>
                <strong>Document URI:</strong> ${e.documentURI}
            `;
            cspViolations.appendChild(violation);
        });

        // Monitor network requests
        const originalFetch = window.fetch;
        const originalXHR = window.XMLHttpRequest;
        
        window.fetch = function(...args) {
            logNetworkRequest('FETCH', args[0]);
            return originalFetch.apply(this, args);
        };

        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            logNetworkRequest('XHR', url);
            return originalXHROpen.apply(this, [method, url, ...args]);
        };

        function logNetworkRequest(type, url) {
            const networkLog = document.getElementById('network-log');
            const logEntry = document.createElement('div');
            logEntry.style.fontSize = '12px';
            logEntry.style.margin = '2px 0';
            logEntry.style.padding = '5px';
            logEntry.style.backgroundColor = '#f8f9fa';
            logEntry.style.borderRadius = '3px';
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${type}: ${url}`;
            networkLog.appendChild(logEntry);
            
            // Keep only last 10 entries
            while (networkLog.children.length > 10) {
                networkLog.removeChild(networkLog.firstChild);
            }
        }

        // Test functions
        window.testFirebaseConnection = async function() {
            const status = document.getElementById('firebase-status');
            status.className = 'status info';
            status.innerHTML = '🔄 Testing Firebase connection...';
            
            try {
                // This would normally import Firebase, but for testing we'll just simulate
                status.className = 'status success';
                status.innerHTML = '✅ Firebase connection test passed (simulated)';
            } catch (error) {
                status.className = 'status error';
                status.innerHTML = `❌ Firebase connection failed: ${error.message}`;
            }
        };

        window.testReeflexFunction = async function() {
            const status = document.getElementById('reeflex-status');
            status.className = 'status info';
            status.innerHTML = '🔄 Testing ReeFlex function...';
            
            try {
                // Simulate calling the ReeFlex function
                status.className = 'status success';
                status.innerHTML = '✅ ReeFlex function test passed (simulated - requires authentication)';
            } catch (error) {
                status.className = 'status error';
                status.innerHTML = `❌ ReeFlex function failed: ${error.message}`;
            }
        };

        window.testAdminNotification = async function() {
            const status = document.getElementById('notification-status');
            const btn = document.getElementById('notification-btn');
            
            btn.disabled = true;
            status.className = 'status info';
            status.innerHTML = '🔄 Testing admin notification system...';
            
            try {
                // Simulate the notification process
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                status.className = 'status success';
                status.innerHTML = '✅ Admin notification system test passed (simulated)';
            } catch (error) {
                status.className = 'status error';
                status.innerHTML = `❌ Admin notification failed: ${error.message}`;
            } finally {
                btn.disabled = false;
            }
        };

        // Initial status
        setTimeout(() => {
            const cspStatus = document.getElementById('csp-status');
            if (cspStatus.innerHTML.includes('No CSP violations')) {
                cspStatus.innerHTML = '✅ No CSP violations detected after 3 seconds. System appears to be working correctly!';
            }
        }, 3000);
    </script>
</body>
</html>
