// Test script to verify Stripe webhook configuration
// Run this to test the webhook endpoint and get configuration details

const https = require('https');

console.log('=== STRIPE WEBHOOK CONFIGURATION TEST ===\n');

// Test webhook endpoint availability
const webhookUrl = 'https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook';
console.log('1. Testing webhook endpoint availability...');
console.log('   URL:', webhookUrl);

const testRequest = https.get(webhookUrl, (res) => {
  console.log('   Status:', res.statusCode);
  console.log('   Status Message:', res.statusMessage);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('   Response:', data);
    
    if (res.statusCode === 400 && data.includes('No Stripe signature')) {
      console.log('   ✅ Webhook endpoint is working correctly (rejecting unsigned requests)');
    } else {
      console.log('   ❌ Unexpected response from webhook endpoint');
    }
    
    console.log('\n2. Stripe Dashboard Configuration Required:');
    console.log('   Navigate to: https://dashboard.stripe.com/webhooks');
    console.log('   Add endpoint URL:', webhookUrl);
    console.log('   Select events: checkout.session.completed');
    console.log('   Webhook secret: whsec_rEz20Kue3bkGHfmnaZilP01s614ULBQb');
    
    console.log('\n3. Test stripeApi endpoint...');
    testStripeApi();
  });
});

testRequest.on('error', (err) => {
  console.log('   ❌ Error testing webhook:', err.message);
});

function testStripeApi() {
  const apiUrl = 'https://us-central1-h1c1-798a8.cloudfunctions.net/stripeApi';
  console.log('   URL:', apiUrl);
  
  const testApiRequest = https.get(apiUrl, (res) => {
    console.log('   Status:', res.statusCode);
    console.log('   Status Message:', res.statusMessage);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('   Response:', data);
      
      if (res.statusCode === 500 && data.includes('Internal server error')) {
        console.log('   ✅ stripeApi endpoint is deployed (expects POST to /create-checkout-session)');
      } else {
        console.log('   ❌ Unexpected response from stripeApi endpoint');
      }
      
      console.log('\n=== TEST COMPLETE ===');
      console.log('Next steps:');
      console.log('1. Configure webhook in Stripe Dashboard with the URL above');
      console.log('2. Test payment flow from frontend');
      console.log('3. Monitor Firebase Functions logs for webhook events');
    });
  });
  
  testApiRequest.on('error', (err) => {
    console.log('   ❌ Error testing stripeApi:', err.message);
  });
}