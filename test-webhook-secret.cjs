// Test script to validate webhook secret update
const crypto = require('crypto');
const https = require('https');

const WEBHOOK_URL = 'https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook';
const NEW_WEBHOOK_SECRET = 'whsec_Ggd0wEt8z8NzRV5kyEyvXH2iB1xrWSZq';

// Create a test payload
const testPayload = JSON.stringify({
  id: 'evt_test_webhook',
  object: 'event',
  type: 'checkout.session.completed',
  data: {
    object: {
      id: 'cs_test_session',
      metadata: {
        orderId: 'test_order_123'
      }
    }
  }
});

// Create signature
const timestamp = Math.floor(Date.now() / 1000);
const payload = timestamp + '.' + testPayload;
const signature = crypto
  .createHmac('sha256', NEW_WEBHOOK_SECRET)
  .update(payload, 'utf8')
  .digest('hex');

const stripeSignature = `t=${timestamp},v1=${signature}`;

console.log('🧪 Testing webhook with new secret...');
console.log('Webhook URL:', WEBHOOK_URL);
console.log('New Secret:', NEW_WEBHOOK_SECRET.substring(0, 20) + '...');
console.log('Signature:', stripeSignature);

// Make the request
const postData = testPayload;
const options = {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData),
    'Stripe-Signature': stripeSignature
  }
};

const req = https.request(WEBHOOK_URL, options, (res) => {
  console.log(`\n📊 Response Status: ${res.statusCode}`);
  console.log('Response Headers:', res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response Body:', data);
    
    if (res.statusCode === 200) {
      console.log('✅ Webhook test PASSED! New secret is working.');
    } else if (res.statusCode === 400 && data.includes('Webhook Error')) {
      console.log('❌ Webhook test FAILED! Signature verification failed.');
      console.log('This means the function is still using the old secret.');
    } else {
      console.log('⚠️ Unexpected response. Check logs for details.');
    }
  });
});

req.on('error', (e) => {
  console.error('❌ Request failed:', e.message);
});

req.write(postData);
req.end();