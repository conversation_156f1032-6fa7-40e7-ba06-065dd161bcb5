// Comprehensive Payment Flow Validation Script
// Tests all aspects of the Stripe payment integration

const https = require('https');
const fs = require('fs');

console.log('🔍 HIVE CAMPUS STRIPE PAYMENT FLOW VALIDATION');
console.log('='.repeat(60));

// Test configuration
const config = {
  stripeApiUrl: 'https://us-central1-h1c1-798a8.cloudfunctions.net/stripeApi',
  webhookUrl: 'https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook',
  testFunctionUrl: 'https://us-central1-h1c1-798a8.cloudfunctions.net/testFunction',
  frontendUrl: 'http://localhost:5174'
};

let testResults = {
  functionsDeployed: false,
  webhookSecurity: false,
  frontendAuth: false,
  configSecurity: false,
  firestoreRules: false,
  errorHandling: false,
  platformFeeLogic: false
};

// Test 1: Verify Functions Deployment
async function testFunctionsDeployment() {
  console.log('\n📋 TEST 1: Functions Deployment Status');
  console.log('-'.repeat(40));
  
  try {
    const response = await makeRequest(config.testFunctionUrl);
    const data = JSON.parse(response.data);
    
    if (data.version === '4.0.0-STRIPE-FOCUSED' && data.status === 'WORKING') {
      console.log('✅ testFunction: Deployed and working');
      testResults.functionsDeployed = true;
    } else {
      console.log('❌ testFunction: Unexpected response');
    }
  } catch (error) {
    console.log('❌ testFunction: Failed to connect');
  }
}

// Test 2: Webhook Security
async function testWebhookSecurity() {
  console.log('\n🔒 TEST 2: Webhook Security Validation');
  console.log('-'.repeat(40));
  
  try {
    await makeRequest(config.webhookUrl);
    console.log('❌ Webhook: Should reject unsigned requests');
  } catch (error) {
    if (error.message.includes('No Stripe signature')) {
      console.log('✅ Webhook: Correctly rejects unsigned requests');
      testResults.webhookSecurity = true;
    } else {
      console.log('❌ Webhook: Unexpected error:', error.message);
    }
  }
}

// Test 3: Frontend Authentication Code
async function testFrontendAuth() {
  console.log('\n🔑 TEST 3: Frontend Authentication Code');
  console.log('-'.repeat(40));
  
  try {
    const hookContent = fs.readFileSync('src/hooks/useStripeCheckout.ts', 'utf8');
    
    if (hookContent.includes('Bearer ${idToken}')) {
      console.log('✅ Frontend: Using proper ID token authentication');
      testResults.frontendAuth = true;
    } else if (hookContent.includes('Bearer ${user.uid}')) {
      console.log('❌ Frontend: Still using user.uid (should be idToken)');
    } else {
      console.log('⚠️  Frontend: Authentication method unclear');
    }
  } catch (error) {
    console.log('❌ Frontend: Could not read useStripeCheckout.ts');
  }
}

// Test 4: Configuration Security
async function testConfigSecurity() {
  console.log('\n🛡️  TEST 4: Configuration Security');
  console.log('-'.repeat(40));
  
  try {
    const indexContent = fs.readFileSync('functions/index.js', 'utf8');
    
    // Check for secure config usage
    if (indexContent.includes('functions.config().stripe')) {
      console.log('✅ Config: Using Firebase Functions config');
      testResults.configSecurity = true;
    } else {
      console.log('❌ Config: Not using Firebase Functions config');
    }
    
    // Check for hardcoded secrets
    if (indexContent.includes('sk_live_') || indexContent.includes('sk_test_')) {
      console.log('❌ Security: Hardcoded Stripe keys found');
    } else {
      console.log('✅ Security: No hardcoded Stripe keys');
    }
  } catch (error) {
    console.log('❌ Config: Could not read functions/index.js');
  }
}

// Test 5: Platform Fee Logic
async function testPlatformFeeLogic() {
  console.log('\n💰 TEST 5: Platform Fee Logic');
  console.log('-'.repeat(40));
  
  try {
    const indexContent = fs.readFileSync('functions/index.js', 'utf8');
    
    if (indexContent.includes('platformFeeRate') && 
        indexContent.includes('textbooks') && 
        indexContent.includes('0.08') && 
        indexContent.includes('0.10')) {
      console.log('✅ Platform Fee: Textbooks 8%, Other items 10%');
      testResults.platformFeeLogic = true;
    } else {
      console.log('❌ Platform Fee: Logic not found or incorrect');
    }
    
    if (indexContent.includes('sellerPayout') && 
        indexContent.includes('platformFee')) {
      console.log('✅ Payout: Seller payout calculation implemented');
    } else {
      console.log('❌ Payout: Seller payout calculation missing');
    }
  } catch (error) {
    console.log('❌ Platform Fee: Could not analyze logic');
  }
}

// Test 6: Error Handling
async function testErrorHandling() {
  console.log('\n🚨 TEST 6: Error Handling and Logging');
  console.log('-'.repeat(40));
  
  try {
    const indexContent = fs.readFileSync('functions/index.js', 'utf8');
    
    const hasLogging = indexContent.includes('console.log') && 
                      indexContent.includes('console.error');
    const hasTryCatch = indexContent.includes('try {') && 
                       indexContent.includes('} catch');
    const hasErrorResponses = indexContent.includes('res.status(500)') || 
                             indexContent.includes('res.status(400)');
    
    if (hasLogging && hasTryCatch && hasErrorResponses) {
      console.log('✅ Error Handling: Comprehensive logging and error handling');
      testResults.errorHandling = true;
    } else {
      console.log('❌ Error Handling: Missing logging or error handling');
    }
  } catch (error) {
    console.log('❌ Error Handling: Could not analyze code');
  }
}

// Test 7: Firestore Rules Analysis
async function testFirestoreRules() {
  console.log('\n🗄️  TEST 7: Firestore Rules Analysis');
  console.log('-'.repeat(40));
  
  try {
    const rulesContent = fs.readFileSync('firestore.rules', 'utf8');
    
    if (rulesContent.includes('match /orders/{orderId}')) {
      console.log('✅ Firestore: Orders collection rules exist');
      
      if (rulesContent.includes('buyerId') && rulesContent.includes('sellerId')) {
        console.log('✅ Firestore: Buyer/seller access control implemented');
        testResults.firestoreRules = true;
      } else {
        console.log('❌ Firestore: Missing buyer/seller access control');
      }
    } else {
      console.log('❌ Firestore: Orders collection rules missing');
    }
  } catch (error) {
    console.log('❌ Firestore: Could not read firestore.rules');
  }
}

// Helper function to make HTTP requests
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve({ statusCode: res.statusCode, data });
        } else {
          reject(new Error(data || `HTTP ${res.statusCode}`));
        }
      });
    });
    
    request.on('error', (err) => {
      reject(err);
    });
    
    request.setTimeout(10000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// Generate final report
function generateReport() {
  console.log('\n📊 VALIDATION SUMMARY');
  console.log('='.repeat(60));
  
  const totalTests = Object.keys(testResults).length;
  const passedTests = Object.values(testResults).filter(result => result).length;
  const passRate = Math.round((passedTests / totalTests) * 100);
  
  console.log(`Tests Passed: ${passedTests}/${totalTests} (${passRate}%)`);
  console.log('');
  
  Object.entries(testResults).forEach(([test, passed]) => {
    const status = passed ? '✅' : '❌';
    const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    console.log(`${status} ${testName}`);
  });
  
  console.log('\n🎯 PRODUCTION READINESS ASSESSMENT');
  console.log('-'.repeat(40));
  
  if (passRate >= 90) {
    console.log('🟢 READY FOR PRODUCTION');
    console.log('All critical systems are operational and secure.');
  } else if (passRate >= 70) {
    console.log('🟡 MOSTLY READY - Minor Issues');
    console.log('Most systems working, but some improvements needed.');
  } else {
    console.log('🔴 NOT READY FOR PRODUCTION');
    console.log('Critical issues need to be resolved before deployment.');
  }
  
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Configure Stripe Dashboard webhook');
  console.log('2. Test with real payment using test card 4242 4242 4242 4242');
  console.log('3. Monitor Firebase Functions logs during testing');
  console.log('4. Verify order status updates in Firestore');
  console.log('5. Test secret code redemption flow');
}

// Run all tests
async function runAllTests() {
  try {
    await testFunctionsDeployment();
    await testWebhookSecurity();
    await testFrontendAuth();
    await testConfigSecurity();
    await testPlatformFeeLogic();
    await testErrorHandling();
    await testFirestoreRules();
    
    generateReport();
  } catch (error) {
    console.error('Test execution failed:', error);
  }
}

// Execute validation
runAllTests();